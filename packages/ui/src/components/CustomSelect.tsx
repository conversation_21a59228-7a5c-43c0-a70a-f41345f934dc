import React from 'react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@mono/ui/select';

// 定义选项类型，支持 number 类型的 value
type Option = {
  label: string;
  value: string | number;
};

// 组件属性类型
interface CustomSelectProps {
  options: Option[];
  value?: string | number;
  onChange?: (value?: string | number) => void;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
  // 增加全部选项的支持
  includeAll?: boolean;
}

export const CustomSelect: React.FC<CustomSelectProps> = ({
  options,
  value,
  onChange,
  placeholder = '请选择',
  disabled = false,
  className = '',
  includeAll,
}) => {
  // 处理值的变化
  const handleValueChange = (newValue: string) => {
    if (!onChange) return;

    // 查找对应的选项，以确定正确的类型
    const selectedOption = options.find(
      (option) => String(option.value) === newValue
    );

    // 如果找到了选项，则使用原始的值类型（字符串或数字）
    if (selectedOption) {
      console.log(selectedOption);
      onChange(selectedOption.value);
    } else {
      // 如果是all选项，则使用undefined
      if (newValue === 'all') {
        onChange(undefined);
      }
    }
  };

  // 默认为全部选项
  if (includeAll && value === undefined) {
    value = 'all';
  }

  // 将当前值转换为字符串，以便在 Select 组件中使用
  const stringValue = value !== undefined ? String(value) : undefined;

  return (
    <Select
      value={stringValue}
      onValueChange={handleValueChange}
      disabled={disabled}
    >
      <SelectTrigger className={className}>
        <SelectValue placeholder={placeholder} />
      </SelectTrigger>
      <SelectContent>
        {includeAll && <SelectItem value="all">全部</SelectItem>}
        {options.map((option) => (
          <SelectItem key={String(option.value)} value={String(option.value)}>
            {option.label}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
};
