import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from './ui/alert-dialog';
import { createContext, useContext, useRef, useState } from 'react';

type DialogInputOption = {
  title: string;
  description?: string;
  okText?: string;
  cancelText?: string;
  onSubmit?: () => void;
  onCancel?: () => void;
  children?: React.ReactNode;
};

export interface DialogType {
  open: ({
    title,
    description,
    okText,
    cancelText,
    onSubmit,
    onCancel,
  }: DialogInputOption) => void;
  close: () => void;
}

export const DialogContext = createContext<DialogType | null>(null);

// 组件内使用的 hook
export const useDialog = () => {
  const context = useContext(DialogContext);
  if (!context) {
    throw new Error('useVipDialog must be used within VipDialogProvider');
  }
  return context;
};

export function DialogProvider({ children }: { children: React.ReactNode }) {
  const [open, setOpen] = useState(false);
  const options = useRef<DialogInputOption>({} as DialogInputOption);
  const methods = {
    open: (option: DialogInputOption) => {
      options.current = option;
      setOpen(true);
    },
    close: () => {
      setOpen(false);
    },
  };
  const {
    title,
    description,
    okText,
    cancelText,
    onSubmit,
    onCancel,
    children: contentDom,
  } = options.current;
  return (
    <DialogContext.Provider value={methods}>
      {children}
      <AlertDialog open={open} onOpenChange={setOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{title}</AlertDialogTitle>
            <AlertDialogDescription>{description}</AlertDialogDescription>
          </AlertDialogHeader>
          {contentDom && <div>{contentDom}</div>}
          <AlertDialogFooter>
            <AlertDialogCancel onClick={onCancel}>
              {cancelText ?? '取消'}
            </AlertDialogCancel>
            <AlertDialogAction onClick={onSubmit}>
              {okText ?? '确定'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </DialogContext.Provider>
  );
}
