<?xml version="1.0" encoding="UTF-8"?>
<svg width="32px" height="32px" viewBox="0 0 32 32" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>编组 7备份 4</title>
    <defs>
        <linearGradient x1="0%" y1="100%" x2="102.08819%" y2="0%" id="linearGradient-1">
            <stop stop-color="#4B9DFF" offset="0%"></stop>
            <stop stop-color="#2121FE" offset="100%"></stop>
        </linearGradient>
        <filter x="-10.5%" y="-14.4%" width="121.0%" height="128.7%" filterUnits="objectBoundingBox" id="filter-2">
            <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="0.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.16 0" type="matrix" in="shadowBlurOuter1" result="shadowMatrixOuter1"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixOuter1"></feMergeNode>
                <feMergeNode in="SourceGraphic"></feMergeNode>
            </feMerge>
        </filter>
        <linearGradient x1="10.3392833%" y1="37.290644%" x2="99.4358904%" y2="65.720073%" id="linearGradient-3">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#D0E4FF" offset="100%"></stop>
        </linearGradient>
        <path d="M15.2234522,0.00427249829 C18.2247076,-0.0772172844 20.5956367,1.00722367 22.3124299,3.23753634 C22.4227057,3.38045688 22.5668161,3.5823008 22.6369916,3.7076697 C22.7309766,3.87566402 22.7673175,4.07500057 22.7673175,4.28060556 C22.7673175,5.06792223 22.1294724,5.70604991 21.3425044,5.70604991 C20.917692,5.70604991 20.5367394,5.51925025 20.2760876,5.22463335 C20.2760876,5.22463335 19.0680666,3.95339274 18.2222013,3.49955733 C16.1094178,2.36622251 13.5592905,2.61946768 11.7134245,4.1389387 C9.8738241,5.65339497 9.13322204,8.10937165 9.83372382,10.4061298 C9.93898706,10.7471332 10.0517691,11.0818682 9.97658107,11.445438 C9.85628023,12.0334181 9.37758313,12.4747166 8.78359772,12.5486843 C8.12946189,12.6289204 7.55302036,12.3129908 7.30114047,11.6924147 C6.96279435,10.8549505 6.76605235,9.97109977 6.77858368,9.07220478 C6.78735562,8.48798572 6.50414739,8.39145167 6.04550043,8.36888527 C4.45151427,8.29241024 2.98910716,9.54484552 2.8387311,11.1244936 C2.68083625,12.7806167 3.81868171,14.2775214 5.42519921,14.5119612 C5.68334477,14.5495719 5.94650286,14.557094 6.20715468,14.557094 C9.21216947,14.5596014 12.2171843,14.5583477 15.222199,14.5596014 C17.6883663,14.5608551 19.5079165,13.5980219 20.6971405,11.4416769 C21.1457624,10.6280328 21.9352367,10.3459528 22.6670668,10.7333427 L22.6670668,10.7333427 C23.3863656,11.1157178 23.6294735,11.9594505 23.1921298,12.7618114 C21.4953867,15.87096 18.8575401,17.3214782 15.3224497,17.3804015 C13.8287143,17.4054753 12.336232,17.3841626 10.8424965,17.3841626 L10.8424965,17.3841626 L10.8424965,17.3578351 C9.06931222,17.3578351 7.2911154,17.4405786 5.52419678,17.3402835 C2.13697619,17.1484691 -0.302875265,14.2123295 0.0304583186,10.8499357 C0.357526233,7.54897269 3.36003475,5.16821734 6.69211745,5.60951586 C7.15703008,5.67094662 7.32369688,5.55309986 7.51542634,5.14690463 C9.05176835,1.89734283 11.6445021,0.100806549 15.2234522,0.00427249829 Z M22.4653123,6.65258508 C23.2245313,6.65258508 23.84,7.26832643 23.84,8.02788187 C23.84,8.78743732 23.2245313,9.40317867 22.4653123,9.40317867 C21.7060932,9.40317867 21.0906245,8.78743732 21.0906245,8.02788187 C21.0906245,7.26832643 21.7060932,6.65258508 22.4653123,6.65258508 Z" id="path-4"></path>
        <linearGradient x1="83.6355907%" y1="43.5484484%" x2="0%" y2="43.5484484%" id="linearGradient-6">
            <stop stop-color="#2064B7" offset="0%"></stop>
            <stop stop-color="#2064B7" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="编组-7备份-4">
            <rect id="矩形" fill="url(#linearGradient-1)" fill-rule="nonzero" x="0" y="0" width="32" height="32" rx="8.625"></rect>
            <g id="编组-5" filter="url(#filter-2)" transform="translate(4.160000, 7.143694)">
                <mask id="mask-5" fill="white">
                    <use xlink:href="#path-4"></use>
                </mask>
                <use id="形状结合" fill="url(#linearGradient-3)" fill-rule="nonzero" xlink:href="#path-4"></use>
                <path d="M7.4490025,5.30026939 C6.99217219,6.04759074 6.76375703,7.25695127 6.76375703,8.92835097 C6.76375703,10.5997507 5.77300292,10.7580266 3.79149469,9.40317867 L4.15372674,4.96055243 L7.4490025,5.30026939 Z" id="路径-2" fill="url(#linearGradient-6)" fill-rule="nonzero" opacity="0.315143" mask="url(#mask-5)"></path>
            </g>
        </g>
    </g>
</svg>