import { TeamRoute } from '@/components/team';
import { createFileRoute } from '@tanstack/react-router';

type Search = {
  tId?: number;
  tName?: string;
  invitationCode?: string;
  from?: string;
  phone?: string;
  salesType?: string;
  expiredStartTime?: number;
  expiredEndTime?: number;
};

export const Route = createFileRoute('/_auth/team')({
  validateSearch: (search: Record<string, unknown>): Search => {
    return {
      tId: search?.tId as number,
      tName: search?.tName as string,
      invitationCode: search?.invitationCode as string,
      from: search?.from as string,
      phone: search?.phone as string,
      salesType: search?.salesType as string,
      expiredStartTime: search?.expiredStartTime as number,
      expiredEndTime: search?.expiredEndTime as number,
    };
  },
  component: TeamRoute,
});
