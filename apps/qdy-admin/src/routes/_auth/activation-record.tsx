import { createFileRoute } from '@tanstack/react-router';
import { ActivationRecordPage } from '@/components/coupon/activationRecordPage';

type Search = {
  couponId?: number;
};

export const Route = createFileRoute('/_auth/activation-record')({
  validateSearch: (search: Record<string, unknown>): Search => {
    return {
      couponId: search?.couponId as number,
    };
  },
  component: ActivationRecordPage,
});
