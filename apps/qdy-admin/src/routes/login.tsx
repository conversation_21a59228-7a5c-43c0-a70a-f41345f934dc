import { createFileRoute, useRouter } from '@tanstack/react-router';
import { z } from 'zod';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@mono/ui/card';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@mono/ui/form';
import { Input } from '@mono/ui/input';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { LoadingButton } from '@/components/loadingButton';
import { useMutation } from '@tanstack/react-query';
import { userAuth, userTwoFactorAuth } from '@/api/user';
import { useUserStore } from '@/store/user';
import { useState } from 'react';
import { LoginAuthenticator } from '@/components/login/loginAuthenticator';

export const Route = createFileRoute('/login')({
  validateSearch: z.object({
    redirect: z.string().optional(),
  }),
  component: LoginComponent,
});

const formSchema = z.object({
  username: z.string().min(1, '请输入账号'),
  password: z.string().min(1, '请输入密码'),
});

function LoginComponent() {
  const router = useRouter();
  const search = Route.useSearch();
  const setUserLogin = useUserStore((state) => state.setUserLogin);
  const [open, setOpen] = useState(false);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      username: '',
      password: '',
    },
  });

  const mutation = useMutation({
    mutationFn: userAuth,
    onSuccess: (data) => {
      console.log(data);
      setUserLogin(data);
      // router.invalidate();

      // 二次验证mfa
      if (data?.initMfa || data?.verifyMfa) {
        setOpen(true);
      } else {
        navTo();
      }
    },
    onError: (error) => {
      // 登录失败
      console.log(error);
    },
  });

  const twoFactorAuthMutation = useMutation({
    mutationFn: userTwoFactorAuth,
    onSuccess: (data) => {
      console.log(data);
      setUserLogin(data);
      setOpen(false);
      navTo();
    },
    onError: (error) => {
      // 二次验证mfa-登录失败
      console.log(error);
    },
  });

  const navTo = () => {
    const mainRoute = search.redirect ?? '/';
    router.history.replace(mainRoute);
  };

  const onSubmit = (values: z.infer<typeof formSchema>) => {
    // Do something with the form values.
    // ✅ This will be type-safe and validated.
    // console.log(values);
    mutation.mutate(values);
  };

  return (
    <main className="grid h-screen place-items-center">
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="w-full max-w-sm"
        >
          <Card className="w-full">
            <CardHeader>
              <CardTitle className="text-2xl">登 录</CardTitle>
              <CardDescription>青豆云后台管理系统</CardDescription>
            </CardHeader>
            <CardContent className="grid gap-4">
              <FormField
                control={form.control}
                name="username"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>账号</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>密码</FormLabel>
                    <FormControl>
                      <Input type="password" {...field} />
                    </FormControl>

                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
            <CardFooter>
              <LoadingButton
                isPending={mutation.isPending}
                disabled={mutation.isPending}
                className="w-full"
                type="submit"
              >
                登 录
              </LoadingButton>
            </CardFooter>
          </Card>
        </form>
      </Form>

      <LoginAuthenticator
        open={open}
        setOpen={setOpen}
        onComplete={(token: string) => {
          const userLoginData = useUserStore.getState().userLogin;
          twoFactorAuthMutation.mutate({
            username: form.getValues().username,
            password: form.getValues().password,
            secret: userLoginData?.initMfa ? userLoginData.secret : undefined,
            token: token,
          });
        }}
      />
    </main>
  );
}
