import { Outlet, createRootRouteWithContext } from '@tanstack/react-router';
// import { TanStackRouterDevtools } from "@tanstack/router-devtools";
// import { QueryClient } from "@tanstack/react-query";
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { AlertBaseWrapper } from '@/components/alertBase.tsx';
import { DialogBaseWrapper } from '@/components/dialogBase';
export const Route = createRootRouteWithContext()({
  component: RootComponent,
});

function RootComponent() {
  return (
    <>
      <Outlet />
      <ReactQueryDevtools buttonPosition="bottom-right" />
      <AlertBaseWrapper />
      <DialogBaseWrapper />
      {/* <TanStackRouterDevtools position="top-right" /> */}
    </>
  );
}
