import { Order, OrderStatus, OrderType, SaleType } from '@/types/order';

export const orderStatusMap: Record<OrderStatus, string> = {
  pending: '待支付',
  success: '已完成',
  canceled: '已取消',
  refund: '已退费',
};

export const orderTypeMap: Record<OrderType, string> = {
  create: '开通',
  renew: '续费',
  upgrade: '升级',
  gift: '赠送',
};

export const orderSourceMap: Record<Order['type'], string> = {
  online: '线上订单',
  system: '系统订单',
};

export const payTypeMap: Record<Order['payType'], string> = {
  wechat: '微信支付',
  alipay: '支付宝支付',
  corporateTransfer: '对公转账',
  iosPay: 'ios支付',
};

export const saleTypeMap: Record<SaleType, string> = {
  NotBuy: '未购买',
  FirstBuy: '新购',
  ReBuy: '复购',
  Buy: '已购买',
};
