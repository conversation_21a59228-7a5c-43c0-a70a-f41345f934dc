import { UserInfo, UserLogin } from '@/types/user';
import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';

type UserState = {
  user?: UserInfo;
  userLogin?: UserLogin;
  setUser: (user?: UserInfo) => void;
  setUserLogin: (userLogin?: UserLogin) => void;
};

export const useUserStore = create<UserState>()(
  persist(
    immer((set) => ({
      user: undefined,
      setUser: (user) =>
        set({
          user,
        }),
      userLogin: undefined,
      setUserLogin: (userLogin) => set({ userLogin }),
    })),
    {
      name: 'qdy-admin-user',
      storage: createJSONStorage(() => localStorage),
    }
  )
);
