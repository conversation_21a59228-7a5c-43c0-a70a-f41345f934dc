export type Channel = {
  code: string;
  createTime: number;
  id: number;
  name: string;
  orderCount: number;
  orderTotal: number;
  status: boolean;
  userCount: number;
  ChannelAdminUser: string;
  channelUserId: number;
};

export type ChannelBase = Pick<Channel, 'name' | 'code'> & {
  couponId?: number;
  password?: string;
  channelUserId?: number;
};

export type ChannelParams = {
  name?: string;
  startTime?: number;
  endTime?: number;
};
