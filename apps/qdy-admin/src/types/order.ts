import { Channel } from './user';

type SourceType = 'online' | 'system';

export type PayType = 'wechat' | 'alipay' | 'corporateTransfer' | 'iosPay';

//  订单号 | 手机号 | 版本 | 账号数 | 团队人数 | 生效时间 | 到期时间 | 支付时间 | 订单金额 | 订单来源(线上/线下)
export type Order = {
  createTime: number;
  id: number;
  interestCount: number;
  month: number;
  orderNo: string;
  payAmount: number;
  dueAmount: number;
  payTime: number;
  payType: PayType;
  phone: string;
  price: number;
  remark: string;
  teamId: number;
  toTime: number;
  type: SourceType;
  orderStatus: OrderStatus;
  orderType: OrderType;
  salesType: SaleType;
  vipId: number;
  fromTime: number;
  freeMonth: number;
  team: Team;
  vipMonth: number;
  channel?: Channel;
};

export interface VipOrderData {
  commentDosageLimit: number;
  createTime: Date;
  expirationTime: Date;
  fansGroupManageLimit: number;
  groupDosageLimit: number;
  platformAccountNumberLimit: number;
  signDosageLimit: number;
  teamMemberNumberLimit: number;
  uploadImageLimit: number;
}

export type OrderParams = {
  /**
   * 到期时间
   */
  orderEndTime?: number;
  /**
   * 订单号
   */
  orderNo?: string;
  /**
   * 开始时间
   */
  orderStartTime?: number;
  /**
   * 订单状态
   */
  orderStatus?: string;
  /**
   * 页码 <默认 1>
   */
  phone?: string;
  /**
   * 支付开始时间
   */
  payEndTime?: number;
  /**
   * 支付开始时间
   */
  payStartTime?: number;
  channelId?: string;
  payAmount?: number;
  orderType?: string;
  salesType?: string;
  invitationCode?: string;
};

/**
 * VipCreateDTO
 */
export type OrderCreateDTO = {
  /**
   * 权益包数量
   */
  interestCount: number;
  /**
   * 权益包id
   */
  interestId: number;
  /**
   * 月份数量
   */
  month: number;
  /**
   * 实付金额
   */
  payAmount: number;
  teamId: number;
  isPay: boolean;
  remark?: string;
  /**
   * 自定义天数
   */
  days: number;
};

/**
 * Team
 */
interface Team {
  id: number;
  name: string;
  invitationCode: string;
}

export type OrderStatus = 'pending' | 'success' | 'canceled' | 'refund';

export type OrderType = 'renew' | 'create' | 'upgrade' | 'gift';

export type SaleType = 'NotBuy' | 'FirstBuy' | 'ReBuy' | 'Buy';

export interface VipData {
  interests: OrderInterestResponse[];
  vipOften: OrderInterestVipOftenDTO[];
}

/**
 * OrderInterestResponse
 */
export interface OrderInterestResponse {
  id: number;
  memberCount: number;
  messageCount: number;
  platformAccountCount: number;
  price: number;
}

/**
 * OrderInterestVipOftenDTO
 */
export interface OrderInterestVipOftenDTO {
  mount: number;
  present: number;
}

export interface OrderDetail {
  orderInfo: Omit<Order, 'remainingTimeInSeconds'> & {
    teamName: string;
    creatorName: string;
    remainingDays: number;
    giftDays: number;
    days?: number;
    invitationCode?: string;
    isGiftOrder?: boolean;
  };
  vipInfo: VipInfo;
}

/**
 * OrderInfo
 */
// export interface OrderInfo {
//   dueAmount: number;
//   fromTime: number;
//   orderNo: string;
//   orderStatus: OrderStatus;
//   payAmount: number;
//   payTime: number;
//   payType: string;
//   teamName: string;
//   type: 'online' | 'system';
// }

/**
 * VipInfo
 */
export interface VipInfo {
  expirationTime: number;
  freeMonth: number;
  interestCount: number;
  messageCount: number;
  month: number;
  platformAccountCount: number;
  teamMemberCount: number;
  giftDays: number;
}

export interface OrderPriceReq {
  couponId?: number;
  /**
   * 权益包数量
   */
  interestCount?: number;
  /**
   * 权益包id
   */
  interestId: number;
  /**
   * 月份数量
   */
  month?: number;
  /**
   * 订单类型(create:开通,upgrade:升级,renew:续费)
   */
  orderType: OrderType;
  /**
   * 团队id
   */
  teamId: number;
  /**
   * 天数
   */
  days?: number;
}
