export type WechatSearchParams = {
  startTime?: string;
  endTime?: string;
  area?: string;
};

export interface Wechat {
  nickName: string;
  headImgUrl: string;
  wxid: string;
  appId: string;
  regionId: string;
  createTime: string;
  updateTime: string;
  loginTime: string;
  regionName: string;
}

export interface OnlineCount {
  onlineCount: number;
  statData: Array<{
    startDay: string;
    wxCount: number;
  }>;
}

export interface OfflineCount {
  onlineCount: number;
  offlineCount: number;
  statData: Array<{
    offlineDate: string;
    offlineCount: number;
  }>;
}

export interface LoginRecord {
  nickName: string;
  headImgUrl: string;
  wxid: string;
  appId: string;
  regionId: string;
  logoutTime: string;
  loginTime: string;
  onlineDuration: string;
}
