// coupon 名称 ｜ 满足金额 ｜ 减免金额 ｜ 创建时间
export type Coupon = {
  activeAmount: number;
  createTime: Date;
  creatorName: string;
  discountAmount: number;
  id: number;
  minimumSpendingAmount: number;
  name: string;
  postAmount: number;
  expireDaysNum: number;
};

export type CouponBase = Pick<
  Coupon,
  'name' | 'minimumSpendingAmount' | 'discountAmount' | 'expireDaysNum'
> & {
  id?: Coupon['id'];
};

export type ActivationRecordParams = {
  couponId?: number;
  phone?: string;
  teamName?: string;
  teamId?: number;
  channelId?: number;
  createTimeStart?: number;
  createTimeEnd?: number;
  castTimeStart?: number;
  castTimeEnd?: number;
  status?: number;
};

export type ActivationRecord = {
  id: number;
  phone: string;
  channelId: number;
  channelName: string;
  teamId: number;
  teamName: string;
  status: number;
  createTime: string;
  castTime: string;
  expireTime: string;
  orderNo: string;
};
