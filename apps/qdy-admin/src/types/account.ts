export type AccountParams = {
  name?: string;
  teamId?: string;
  platform?: number;
  startTime?: number;
  endTime?: number;
  status?: string;
  invitationCode?: string;
};

export type Account = {
  name: string;
  platformAccountId: string;
  avatar: string;
  openId: string;
  expiresIn: number;
  refreshExpiresIn: number;
  id: number;
  teamId: number;
  teamName: string;
  username: string;
  accountRole: string;
  isGreeting: string;
  platform: number;
  scopes: string;
  status: number;
  appId: string;
  regionId: string;
  remark: string;
  createTime: number;
  expiresTime: number;
  invitationCode: string;
  isBind: boolean;
};
