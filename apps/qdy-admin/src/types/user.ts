export type UserLogin = {
  verifyMfa?: boolean;
  initMfa?: boolean;
  qrcode?: string;
  secret?: string;
  authorization: string;
};

export type UserInfo = {
  avatar: string;
  id: string;
  nickname: string;
  username: string;
  role: number;
};

export type UserLoginReq = {
  username: string;
  password: string;
};

export type UserTwoFactorReq = {
  username: string;
  password: string;
  secret?: string;
  token: string;
};

/**
 * MemberResponse
 */
export interface User {
  avatar: string;
  channel: Channel;
  createTime: number;
  id: number;
  name: string;
  phone: string;
}

/**
 * Channel
 */
export interface Channel {
  id: number;
  name: string;
}

export type UserParams = {
  phone?: string;
  startTime?: number;
  endTime?: number;
  channelId?: string;
};
