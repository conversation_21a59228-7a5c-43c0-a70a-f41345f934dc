import All from '@/assets/platform-all.png';
import DyLogo from '@/assets/dy.png';
import WxLogo from '@/assets/wx.png';
import WbLogo from '@/assets/wb.png';
import KsLogo from '@/assets/ks.png';
import XhsLogo from '@/assets/xhs.png';

export type PlatformType =
  | 'wechat'
  | 'douyin'
  | 'kuaishou'
  | 'weibo'
  | 'xiaohongshu';

export const platformMap = [
  {
    key: 'all',
    value: '全部',
    icon: All,
  },
  {
    key: '0',
    value: '抖音',
    icon: DyLogo,
  },
  {
    key: '1',
    value: '视频号',
    icon: WxLogo,
  },
  {
    key: '2',
    value: '微博',
    icon: WbLogo,
  },
  {
    key: '3',
    value: '快手',
    icon: KsLogo,
  },
  {
    key: '4',
    value: '小红书',
    icon: XhsLogo,
  },
];

export const getPlatform = {
  douyin: 0,
  wechat: 1,
  weibo: 2,
  kuaishou: 3,
  xiaohongshu: 4,
};

export const getPlatformName = {
  douyin: '抖音',
  wechat: '微信',
  weibo: '微博',
  kuaishou: '快手',
  xiaohongshu: '小红书',
};

export const getPlatformLogo = (platform: number) => {
  let iconLogo = null;
  switch (platform) {
    case 0:
      iconLogo = DyLogo;
      break;
    case 1:
      iconLogo = WxLogo;
      break;
    case 2:
      iconLogo = WbLogo;
      break;
    case 3:
      iconLogo = KsLogo;
      break;
    case 4:
      iconLogo = XhsLogo;
      break;
  }

  return iconLogo;
};

export const isDy = (platformType: PlatformType | string) => {
  return platformType === 'douyin';
};

export const isWx = (platformType: PlatformType | string) => {
  return platformType === 'wechat';
};

export const isWb = (platformType: PlatformType | string) => {
  return platformType === 'weibo';
};

export const isKs = (platformType: PlatformType | string) => {
  return platformType === 'kuaishou';
};

export const isXhs = (platformType: PlatformType | string) => {
  return platformType === 'xiaohongshu';
};

export const douyinPlatform = (platform: number) => {
  return platform === 0;
};

export const wechatPlatform = (platform: number) => {
  return platform === 1;
};

export const weiboPlatform = (platform: number) => {
  return platform === 2;
};

export const kuaishouPlatform = (platform: number) => {
  return platform === 3;
};

export const xiaohongshuPlatform = (platform: number) => {
  return platform === 4;
};
