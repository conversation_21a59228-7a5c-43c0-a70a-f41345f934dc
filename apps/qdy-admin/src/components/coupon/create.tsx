import { Button } from '@mono/ui/button';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON>alog<PERSON>ooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@mono/ui/dialog';
import { Input } from '@mono/ui/input';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@mono/ui/form';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { createCoupon, updateCoupon } from '@/api/coupon';
import { LoadingButton } from '@/components/loadingButton.tsx';
import { useEffect, useRef, useState } from 'react';
import { Coupon } from '@/types/coupon';
import NumberInput from '../NumberInput';
const formSchema = z.object({
  name: z.string().min(1, { message: '请输入名称' }),
  minimumSpendingAmount: z.number().min(1, { message: '请输入最小消费金额' }),
  discountAmount: z.number().min(1, { message: '请输入折扣金额' }),
  // 领取后有效期
  expireDaysNum: z.number().min(1, { message: '请输入有效期' }),
});

export function Create({ coupon }: { coupon?: Coupon }) {
  const [open, setOpen] = useState(false);
  const type = useRef<'update' | 'add'>('add');
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: '',
    },
  });
  const queryClient = useQueryClient();
  const editMutation = useMutation({
    mutationFn: updateCoupon,
    onSuccess: () => {
      setOpen(false);
      queryClient.refetchQueries({ queryKey: ['getCoupons'] });
    },
  });

  const mutation = useMutation({
    mutationFn: createCoupon,
    onSuccess: () => {
      setOpen(false);
      queryClient.refetchQueries({ queryKey: ['getCoupons'] });
    },
    onError: (error) => {
      // 登录失败
      console.log(error);
    },
  });

  function onSubmit(values: z.infer<typeof formSchema>) {
    // Do something with the form values.
    console.log(values);
    if (type.current === 'update') {
      editMutation.mutate({ id: coupon!.id, ...values });
    } else {
      mutation.mutate(values);
    }
  }

  useEffect(() => {
    if (open) {
      if (type.current === 'add') {
        form.reset({
          name: '',
        });
      }
    } else {
      type.current = 'add';
    }
  }, [form, open]);

  useEffect(() => {
    if (coupon) {
      type.current = 'update';
      form.reset(coupon);
      setOpen(true);
    }
  }, [coupon, form]);

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button>新建优惠券</Button>
      </DialogTrigger>
      <DialogContent className="">
        <Form {...form}>
          <form
            className="flex flex-col gap-5"
            onSubmit={form.handleSubmit(onSubmit)}
          >
            <DialogHeader>
              <DialogTitle>
                {type.current === 'update' ? '编辑优惠券' : '新建优惠券'}
              </DialogTitle>
            </DialogHeader>
            <div className="flex flex-col gap-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>名称</FormLabel>
                    <Input id="name" placeholder="输入名称" {...field} />
                  </FormItem>
                )}
              />
              <div className="flex flex-col gap-2">
                <div className="text-sm font-medium">满减金额</div>
                <div className="flex items-center gap-2">
                  <FormField
                    control={form.control}
                    name="minimumSpendingAmount"
                    render={({ field }) => (
                      <FormItem>
                        <FormControl>
                          <NumberInput
                            placeholder="输入最小消费金额"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  -
                  <FormField
                    control={form.control}
                    name="discountAmount"
                    render={({ field }) => (
                      <FormItem>
                        <FormControl>
                          <NumberInput placeholder="输入折扣金额" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>
              <FormField
                control={form.control}
                name="expireDaysNum"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>有效期</FormLabel>
                    <FormControl>
                      <div className="flex items-center gap-2 text-sm">
                        领取后
                        <NumberInput className="w-20" {...field} />
                        天失效
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <DialogFooter>
              <LoadingButton
                isPending={mutation.isPending}
                disabled={mutation.isPending}
                type="submit"
              >
                {type.current === 'update' ? '更新' : '创建'}
              </LoadingButton>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
