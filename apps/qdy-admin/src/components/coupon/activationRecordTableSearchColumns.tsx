import { ColumnDef } from '@tanstack/react-table';
import { formatDate, formatYearMonthDay } from '@/lib/utils/day';
import { ActivationRecord } from '@/types/coupon';

export function getTableColumns(): Array<ColumnDef<ActivationRecord>> {
  return [
    {
      header: '手机号',
      accessorKey: 'phone',
      cell: ({ row }) => {
        const { phone } = row.original;
        return <span>{phone}</span>;
      },
    },
    {
      header: '渠道',
      accessorKey: 'channelName',
      cell: ({ row }) => {
        const { channelName } = row.original;
        return <span>{channelName || '-'}</span>;
      },
    },
    {
      header: '团队名称',
      accessorKey: 'teamName',
      cell: ({ row }) => {
        const { teamName } = row.original;
        return <span>{teamName}</span>;
      },
    },
    {
      header: '使用状态',
      accessorKey: 'status',
      cell: ({ row }) => {
        const { status, orderNo } = row.original;
        return (
          status === 0 ? <span>未使用</span>
          : status === 1 ?
            <div className="flex flex-col">
              <span>已使用</span>
              <span>
                订单号：<span className="text-sky-500">{orderNo ?? '-'}</span>
              </span>
            </div>
          : status === 2 ? <span>已过期</span>
          : <></>
        );
      },
    },
    {
      header: '发放时间',
      accessorKey: 'createTime',
      cell: ({ row }) => {
        const { createTime } = row.original;
        return (
          <span className="text-muted-foreground">
            {formatDate(createTime)}
          </span>
        );
      },
    },
    {
      header: '到期日期',
      accessorKey: 'expireTime',
      cell: ({ row }) => {
        const { expireTime } = row.original;
        return (
          <span className="text-muted-foreground">
            {formatYearMonthDay(expireTime)}
          </span>
        );
      },
    },
    {
      header: '使用时间',
      accessorKey: 'castTime',
      cell: ({ row }) => {
        const { castTime } = row.original;
        return castTime ?
            <span className="text-muted-foreground">
              {formatDate(castTime)}
            </span>
          : '-';
      },
    },
  ];
}
