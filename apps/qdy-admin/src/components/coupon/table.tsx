import TableData from '@/components/table.tsx';
import { usePageQuery } from '@/hooks/usePageQuery.ts';
import { getCoupons } from '@/api/coupon.ts';
import { ColumnDef } from '@tanstack/react-table';
import { Coupon } from '@/types/coupon';

export function TableBase({
  columns,
}: {
  columns: ColumnDef<Coupon, unknown>[];
}) {
  const { query, pagination, setPagination } = usePageQuery(
    ['getCoupons'],
    getCoupons
  );
  return (
    <TableData
      columns={columns}
      data={query.data?.data}
      rowCount={query.data?.total ?? 0}
      pagination={pagination}
      setPagination={setPagination}
    />
  );
}
