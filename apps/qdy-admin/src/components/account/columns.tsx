import { ColumnDef } from '@tanstack/react-table';
import { formatDate } from '@/lib/utils/day';
import { Button } from '@mono/ui/button';
import { Account } from '@/types/account';
import { Avatar, AvatarFallback, AvatarImage } from '@mono/ui/avatar';
import { PlatformBadge } from '../platformBadge';
import { Link } from '@tanstack/react-router';
import { wechatPlatform } from '@/lib/platform';
import DropdownMenuComponent from '../DropdownMenuComponent.tsx';
import { DropdownMenuItem } from '@mono/ui/dropdown-menu';
import { accoutStatusView } from './accoutStatus.tsx';

export function getTableColumns(
  unAuthorize?: (platformAccountId: number) => void
): Array<ColumnDef<Account>> {
  return [
    {
      header: '账号名称',
      accessorKey: 'platformAccount',
      size: 250,
      cell: ({ row }) => {
        const { name, avatar, platform } = row.original;
        return (
          <div className="flex items-center gap-3">
            <div className="flex-shrink-0 relative">
              <Avatar className="w-9 h-9">
                <AvatarImage src={avatar} />
                <AvatarFallback className="text-xs">
                  {name?.substring(0, 2)}
                </AvatarFallback>
              </Avatar>
              {platform !== undefined && <PlatformBadge value={platform} />}
            </div>
            <span className="text-muted-foreground">{name}</span>
          </div>
        );
      },
    },
    {
      header: '状态',
      accessorKey: 'status',
      cell: ({ row }) => {
        return <>{accoutStatusView(row.original, false)}</>;
      },
    },
    {
      header: '添加时间',
      accessorKey: 'createTime',
      cell: ({ row }) => {
        const { createTime } = row.original;
        return (
          <span className="text-muted-foreground">
            {formatDate(createTime)}
          </span>
        );
      },
    },
    {
      header: '所在团队',
      accessorKey: 'team',
      cell: ({ row }) => {
        const { teamName, invitationCode } = row.original;
        return (
          <Button variant="link" className="!p-0">
            <Link to="/team" search={{ invitationCode: invitationCode }}>
              <p className="text-muted-foreground text-xs text-start">
                {teamName}
              </p>
              <p className="text-muted-foreground text-xs text-start mt-1">
                {invitationCode}
              </p>
            </Link>
          </Button>
        );
      },
    },
    {
      header: '操作',
      accessorKey: 'action',
      cell: ({ row }) => {
        const { id, platform, expiresTime } = row.original;
        return wechatPlatform(platform) && expiresTime > Date.now() ?
            <DropdownMenuComponent className="!w-max !h-max">
              <DropdownMenuItem
                onClick={() => {
                  unAuthorize?.(id);
                }}
              >
                解除授权
              </DropdownMenuItem>
            </DropdownMenuComponent>
          : <span>-</span>;
      },
    },
  ];
}
