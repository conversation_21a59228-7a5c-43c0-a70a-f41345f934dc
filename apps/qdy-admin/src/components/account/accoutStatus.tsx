import { Account } from '@/types/account';
import {
  formatYearMonthDay,
  isExpiredFun,
  reduceDuration,
} from '@/lib/utils/day';
import {
  douyinPlatform,
  weiboPlatform,
  xiaohongshuPlatform,
} from '@/lib/platform';

// 账号状态
export const accoutStatusView = (
  account: Account,
  showExpiresTime: boolean
) => {
  if (!account) return <></>;
  const { status, expiresTime, expiresIn, platform, scopes, isBind } = account;

  if (status === 1) {
    return (
      <div className="flex text-xs text-muted-foreground gap-1 mt-2">
        <div className="flex items-center">
          <div className="w-1.5 h-1.5 bg-[#EA3636] rounded-full"></div>
          <span className="text-xs text-destructive ml-2">冻结</span>
        </div>
      </div>
    );
  } else if (expiresTime < Date.now()) {
    return (
      <div className="flex mt-2">
        <div className="flex items-center">
          <div className="w-1.5 h-1.5 bg-[#EA3636] rounded-full"></div>
          <span className="text-xs text-destructive ml-2">已失效</span>
        </div>
      </div>
    );
  } else if (xiaohongshuPlatform(platform) && !isBind) {
    return (
      <div className="flex mt-2">
        <div className="flex items-center">
          <div className="w-1.5 h-1.5 bg-[#EA3636] rounded-full"></div>
          <span className="text-xs text-destructive ml-2">未绑定聚光</span>
        </div>
      </div>
    );
  } else if (scopes?.length > 0 && expiresIn !== 0) {
    return (
      <div className="flex mt-2">
        <div className="flex items-center">
          <div className="w-1.5 h-1.5 bg-[#EA3636] rounded-full"></div>
          <span className="text-xs text-destructive ml-2">授权异常</span>
        </div>
      </div>
    );
  } else if (expiresIn === 0) {
    return (
      <div className="flex mt-2">
        <div className="flex items-center">
          <div className="w-1.5 h-1.5 bg-[#EA3636] rounded-full"></div>
          <span className="text-xs text-destructive ml-2">
            {douyinPlatform(platform) || weiboPlatform(platform) ?
              '已解除授权'
            : '已失效'}
          </span>
        </div>
      </div>
    );
  } else if (expiresTime > Date.now()) {
    if (
      weiboPlatform(platform) &&
      isExpiredFun(reduceDuration(5, 'day', expiresTime))
    ) {
      return (
        <div className="flex mt-2">
          <div className="flex items-center">
            <div className="w-1.5 h-1.5 bg-[#EA3636] rounded-full"></div>
            <span className="text-xs text-destructive ml-2">即将过期</span>
          </div>
        </div>
      );
    } else {
      return (
        <div className="flex items-center gap-2 mt-2">
          <div className="w-1.5 h-1.5 bg-[#16A34A] rounded-full"></div>
          <span className="text-xs text-foreground">正常</span>
          {showExpiresTime && (
            <span className="text-[#666666] text-xs font-normal ml-1">
              授权到期日期:
              <span className="text-foreground ml-1">
                {douyinPlatform(platform) ?
                  formatYearMonthDay(expiresTime)
                : '长期'}
              </span>
            </span>
          )}
        </div>
      );
    }
  } else {
    return <></>;
  }
};
