import { createColumnHelper } from '@tanstack/react-table';
import { formatDate } from '@/lib/utils/day';
import { Button } from '@mono/ui/button';
import { AppVersion } from '@/types/app';

const columnHelper = createColumnHelper<AppVersion & { action: string }>();

export const getAppVersionColumns = (onDelete: (item: AppVersion) => void) => {
  return [
    columnHelper.accessor('user.name', {
      cell: (info) => (
        <div className="flex items-center gap-2">
          <span>{info.getValue()}</span>
        </div>
      ),
      header: () => <span>用户</span>,
    }),
    columnHelper.accessor('version', {
      cell: (info) => info.getValue(),
      header: () => <span>版本号</span>,
    }),
    columnHelper.accessor('desc', {
      cell: (info) => info.getValue(),
      header: () => <span>版本公告</span>,
    }),
    columnHelper.accessor('force', {
      cell: (info) => (info.getValue() ? '是' : '否'),
      header: () => <span>强制更新</span>,
    }),
    columnHelper.accessor('type', {
      cell: (info) => info.getValue(),
      header: () => <span>设备类型</span>,
    }),

    columnHelper.accessor('releaseTime', {
      header: () => '发布时间',
      cell: (info) => (
        <span className="text-muted-foreground">
          {formatDate(info.getValue())}
        </span>
      ),
    }),
    columnHelper.accessor('action', {
      cell: (info) => (
        <Button
          variant="link"
          className="w-max p-0 text-foreground"
          onClick={() => {
            onDelete(info.row.original);
          }}
        >
          删除
        </Button>
      ),

      header: () => <span>操作</span>,
    }),
  ];
};
