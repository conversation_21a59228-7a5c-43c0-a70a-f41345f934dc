import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from '@mono/ui/dialog';
import { Button } from '@mono/ui/button';

import { LoadingButton } from '../loadingButton';
import { useForm } from 'react-hook-form';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from '@mono/ui/form';
import { Input } from '@mono/ui/input';
import { RadioGroup, RadioGroupItem } from '@mono/ui/radio-group';
import { Label } from '@mono/ui/label';
import { Textarea } from '@mono/ui/textarea';
import { AppPlatformType, ICreateAppVersion } from '@/types/app';
import { createAppVersion, getAppLastVersion, ossUpload } from '@/api/app';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

export function SetCreate({
  open,
  setOpen,
}: {
  open: boolean;
  setOpen: (open: boolean) => void;
}) {
  const queryClient = useQueryClient();

  const form = useForm<ICreateAppVersion>({
    defaultValues: {
      version: '',
      force: false,
      type: AppPlatformType.Ios,
      url: '',
      desc: '',
    },
  });

  const { data } = useQuery({
    queryKey: ['getAppLastVersion', form.watch('type')],
    queryFn: async () => {
      const type = form.getValues('type') as AppPlatformType;
      const res = await getAppLastVersion(type);
      if (type === AppPlatformType.Ios) {
        form.setValue('url', res.url);
      }

      return res;
    },
  });

  const mutation = useMutation({
    mutationFn: async (value: ICreateAppVersion) => {
      switch (value.type) {
        case AppPlatformType.Ios:
          delete value.file;
          await createAppVersion(value);
          break;
        case AppPlatformType.Android: {
          const res = await ossUpload(
            value.file!,
            `${value.version}.apk`,
            () => {}
          );
          if (res) {
            value.url = res;
            delete value.file;
            await createAppVersion(value);
          }
          break;
        }
      }
    },
    onSuccess: () => {
      setOpen(false);
      form.reset();
      queryClient.refetchQueries({ queryKey: ['getAppVersions'] });
    },
    onError: (error) => {
      console.log(error);
    },
  });

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent className="!max-w-none w-[400px]">
        <DialogHeader>
          <DialogTitle className="text-center">发布版本</DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form
            onSubmit={form.handleSubmit((value) => {
              mutation.mutate(value);
            })}
            className="flex flex-col gap-4"
          >
            <FormField
              control={form.control}
              rules={{ required: true }}
              name="type"
              render={({ field }) => (
                <FormItem className="flex items-center">
                  <FormLabel className="w-[100px] flex-shrink-0">
                    设备类型
                  </FormLabel>
                  <FormControl>
                    <RadioGroup
                      className="flex !mt-0"
                      value={field.value}
                      onValueChange={field.onChange}
                    >
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value={AppPlatformType.Ios} id="r2" />
                        <Label htmlFor="r2">Ios</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem
                          value={AppPlatformType.Android}
                          id="r3"
                        />
                        <Label htmlFor="r3">安卓</Label>
                      </div>
                    </RadioGroup>
                  </FormControl>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              rules={{ required: true }}
              name="version"
              render={({ field }) => (
                <FormItem className="flex items-start">
                  <FormLabel className="w-[100px] flex-shrink-0">
                    版本号
                  </FormLabel>
                  <div className="!mt-0">
                    <FormControl>
                      <Input
                        className="w-full"
                        placeholder="输入版本号"
                        {...field}
                      />
                    </FormControl>
                    <span className="text-[12px]">
                      最新版本: {data?.version}
                    </span>
                  </div>
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="force"
              render={({ field }) => (
                <FormItem className="flex items-center">
                  <FormLabel className="w-[100px] flex-shrink-0">
                    强制更新
                  </FormLabel>
                  <FormControl>
                    <RadioGroup
                      className="flex !mt-0"
                      value={`${Number(field.value)}`}
                      onValueChange={(value) => {
                        field.onChange(Boolean(Number(value)));
                      }}
                    >
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="0" id="r2" />
                        <Label htmlFor="r2">否</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="1" id="r3" />
                        <Label htmlFor="r3">是</Label>
                      </div>
                    </RadioGroup>
                  </FormControl>
                </FormItem>
              )}
            />
            {form.watch('type') === AppPlatformType.Ios && (
              <FormField
                control={form.control}
                name="url"
                rules={{ required: true }}
                render={({ field }) => (
                  <FormItem className="flex items-center">
                    <FormLabel className="w-[100px] flex-shrink-0">
                      Ios更新地址
                    </FormLabel>
                    <Input
                      className="!mt-0 w-full"
                      placeholder="输入Url"
                      {...field}
                    />
                  </FormItem>
                )}
              />
            )}

            {form.watch('type') === AppPlatformType.Android && (
              <FormField
                control={form.control}
                name="file"
                rules={{ required: true }}
                render={({ field }) => (
                  <FormItem className="flex items-center">
                    <FormLabel className="w-[100px] flex-shrink-0">
                      安装包
                    </FormLabel>
                    <Input
                      className="!mt-0"
                      id="picture"
                      type="file"
                      accept="application/vnd.android.package-archive"
                      onChange={(event) => {
                        field.onChange(event.target.files![0]);
                      }}
                    />
                  </FormItem>
                )}
              />
            )}
            <FormField
              control={form.control}
              rules={{ required: true }}
              name="desc"
              render={({ field }) => (
                <FormItem className="flex items-start">
                  <FormLabel className="w-[100px] flex-shrink-0">
                    更新描述
                  </FormLabel>
                  <FormControl>
                    <Textarea
                      {...field}
                      className="!mt-0 min-h-[200px]"
                      placeholder="输入更新描述"
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            <DialogFooter className="flex !justify-between">
              <Button
                className="flex-1"
                onClick={() => {
                  setOpen(false);
                }}
                variant="outline"
              >
                取消
              </Button>
              <LoadingButton
                className="flex-1"
                type="submit"
                isPending={mutation.isPending}
                disabled={mutation.isPending}
              >
                发布
              </LoadingButton>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
