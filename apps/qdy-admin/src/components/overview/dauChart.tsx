import { getTeamsDau } from '@/api/overview';
import { useQuery } from '@tanstack/react-query';
import { Area, AreaChart, CartesianGrid, XAxis } from 'recharts';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@mono/ui/tooltip';
import { CircleHelp } from 'lucide-react';

import { Card, CardContent, CardHeader, CardTitle } from '@mono/ui/card';
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from '@mono/ui/chart';

const chartConfig = {
  count: {
    label: '活跃数',
    color: 'hsl(var(--chart-1))',
  },
} satisfies ChartConfig;

export function DauChart() {
  const { data } = useQuery({
    queryKey: ['getDauData'],
    queryFn: getTeamsDau,
  });
  return (
    <Card className="h-full overflow-hidden flex flex-col flex-1">
      <CardHeader className="flex flex-col items-stretch space-y-0 border-b p-0">
        <div className="flex flex-shrink-0 flex-col justify-center gap-1 px-6 py-4">
          <CardTitle className="flex">
            30日活跃团队数
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <CircleHelp size={16} className="ml-1" />
                </TooltipTrigger>
                <TooltipContent className="bg-black">
                  <p>团队内发送消息数大于10条</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </CardTitle>
        </div>
      </CardHeader>
      <CardContent className="px-2 flex-1 h-0">
        <ChartContainer config={chartConfig} className="h-full w-full">
          <AreaChart
            accessibilityLayer
            data={data?.list}
            margin={{
              left: 12,
              right: 12,
            }}
          >
            <CartesianGrid vertical={false} />
            <XAxis
              type="category"
              dataKey="date"
              tickLine={false}
              axisLine={false}
              tickMargin={8}
            />
            <ChartTooltip
              cursor={false}
              content={<ChartTooltipContent indicator="line" />}
            />
            <Area
              dataKey="count"
              type="natural"
              fill="var(--color-count)"
              fillOpacity={0.4}
              stroke="var(--color-count)"
            />
          </AreaChart>
        </ChartContainer>
      </CardContent>
    </Card>
  );
}
