import { Tabs, TabsList, TabsTrigger } from '@mono/ui/tabs';

export default function TimeTabs({
  days,
  setDays,
}: {
  days: string;
  setDays: (days: string) => void;
}) {
  return (
    <Tabs value={days} onValueChange={setDays}>
      <TabsList>
        <TabsTrigger value="1">昨日</TabsTrigger>
        <TabsTrigger value="2">7日</TabsTrigger>
        <TabsTrigger value="3">30日</TabsTrigger>
      </TabsList>
    </Tabs>
  );
}
