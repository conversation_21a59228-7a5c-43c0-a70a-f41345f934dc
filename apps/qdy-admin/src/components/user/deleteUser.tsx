import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from '@mono/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from '@mono/ui/form';
import { Input } from '@mono/ui/input';
import { Button } from '@mono/ui/button';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { LoadingButton } from '@/components/loadingButton';
import { deleteUser } from '@/api/user';
import { toast } from 'sonner';
import { VisuallyHidden } from '@radix-ui/react-visually-hidden';
import { useEffect } from 'react';

const phoneRegex = /^1[3456789]\d{9}$/;

const zodIsPhone = z
  .string()
  .min(1, {
    message: '手机号码不能为空',
  })
  .refine((value) => phoneRegex.test(value), {
    message: '手机号码格式不正确',
  });

const formSchema = z.object({
  phone: zodIsPhone,
});

export function DeleteUser({
  open,
  setOpen,
  userId,
}: {
  open: boolean;
  setOpen: (open: boolean) => void;
  userId: number;
}) {
  const queryClient = useQueryClient();

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      phone: '',
    },
  });

  const mutation = useMutation({
    mutationFn: (variables: string) => deleteUser(userId, variables),
    onSuccess: () => {
      toast.success('注销成功');
      setOpen(false);
      form.reset();
      queryClient.refetchQueries({
        queryKey: ['getUsers'],
      });
    },
  });

  const onSubmit = (values: z.infer<typeof formSchema>) => {
    // console.log("%c data:\n", "color:#FF7A45", data);
    mutation.mutate(values?.phone);
  };

  useEffect(() => {
    if (!open) {
      form.reset();
    }
  }, [open]);

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <VisuallyHidden>
              <DialogHeader>
                <DialogTitle className="text-center"></DialogTitle>
              </DialogHeader>
            </VisuallyHidden>

            <div className="flex flex-col mt-6 mb-6">
              <FormField
                control={form.control}
                name="phone"
                render={({ field }) => (
                  <div className="flex flex-col">
                    <FormItem className="flex items-center gap-1 space-y-0">
                      <FormControl>
                        <Input
                          type="tel"
                          placeholder="请输入该用户手机号"
                          autoComplete="off"
                          {...field}
                        />
                      </FormControl>
                    </FormItem>
                    <span className="text-xs mt-0.5">
                      注销后该账号的所有权益、账号、团队将清空，
                      团队内成员将自动退出
                    </span>
                    <FormMessage />
                  </div>
                )}
              />
            </div>

            <DialogFooter>
              <Button
                onClick={() => {
                  form.reset();
                  setOpen(false);
                }}
                variant="ghost"
                type="reset"
              >
                取消
              </Button>
              <LoadingButton
                isPending={mutation.isPending}
                disabled={mutation.isPending}
                type="submit"
              >
                确定注销
              </LoadingButton>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
