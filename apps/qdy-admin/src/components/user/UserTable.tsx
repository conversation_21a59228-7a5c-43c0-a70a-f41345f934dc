import TableData from '@/components/table';
import { usePageQuery } from '@/hooks/usePageQuery';
import { getUsers } from '@/api/user';
import { ColumnDef } from '@tanstack/react-table';
import { User, UserParams } from '@/types/user';
import { UserTableSearch } from '@/components/user/UserTableSearch';
import { useSearch } from '@tanstack/react-router';
import { useMemo, useRef, useState } from 'react';

export function UserTable({
  columns,
}: {
  columns: ColumnDef<User, unknown>[];
}) {
  const { channelId } = useSearch({ strict: false });
  const [searchParams, setSearchParams] = useState<UserParams>({
    channelId: channelId ? `${channelId}` : '',
    phone: '',
  });
  const { query, pagination, setPagination } = usePageQuery(
    ['getUsers'],
    getUsers,
    searchParams
  );
  const totalCount = useRef(0);
  const rowCount = useMemo(() => {
    if (query.data) {
      totalCount.current = query.data.total;
      return query.data.total;
    }
    return totalCount.current;
  }, [query.data]);
  return (
    <>
      <UserTableSearch
        onSearch={(values) => {
          setPagination({ pageIndex: 0, pageSize: 20 });
          setSearchParams(values);
        }}
        values={searchParams}
      />
      <TableData
        columns={columns}
        data={query.data?.data}
        rowCount={rowCount}
        pagination={pagination}
        setPagination={setPagination}
      />
    </>
  );
}
