import { Link, useParams } from '@tanstack/react-router';
import { Button } from '@mono/ui/button';
import { ArrowLeftIcon } from 'lucide-react';
import { ScrollArea } from '@mono/ui/scroll-area';
import { Card, CardContent } from '@mono/ui/card';
import { formatMount, formatPrice } from '@/lib/utils.ts';
import { LoadingContainer } from '@/components/loading.tsx';
import { formatDate } from '@/lib/utils/day';
import dayjs from 'dayjs';
import { Badge } from '@mono/ui/badge';
import { Order, PayType as PayTypeDto } from '@/types/order';
import { ReactNode } from 'react';
import { orderStatusMap, payTypeMap, orderSourceMap } from '@/config/order';
import { useOrderDetail } from '@/hooks/useOrderDetail';

export function Row({ label, value }: { label: string; value: ReactNode }) {
  return (
    <li className="flex items-center gap-6 text-sm">
      <div className="w-20 text-muted-foreground">{label}:</div>
      <span className={'line-clamp-1 flex-1'}>{value}</span>
    </li>
  );
}

export function OrderStatus({
  status,
}: {
  status: 'pending' | 'success' | 'canceled' | 'refund';
}) {
  return (
    <Badge
      className={status === 'pending' ? 'bg-[#FFF1D6] text-[#B76E00]' : ''}
      variant={'secondary'}
    >
      {orderStatusMap[status]}
    </Badge>
  );
}

export function OrderType(props: { type: Order['type'] }) {
  return <span>{orderSourceMap[props.type]}</span>;
}

export function OrderPrice(props: { price: number }) {
  return <span>¥{formatPrice(props.price)}</span>;
}

export function PayType(props: { payType: PayTypeDto }) {
  return <span>{payTypeMap[props.payType] || '-'}</span>;
}

export default function OrderDetail() {
  const params = useParams({ strict: false });
  const { data } = useOrderDetail(params.orderNO);
  return (
    <div className="h-full w-full flex flex-col overflow-hidden">
      <div className="flex flex-shrink-0 items-center h-11 pb-4 relative justify-center bg-background border-b">
        <Link to="/order" className="absolute left-2">
          <Button variant="ghost">
            <ArrowLeftIcon className="w-4 h-4 mr-2" />
            返回
          </Button>
        </Link>
        <div className="text-lg font-bold">订单详情</div>
      </div>
      <ScrollArea className="flex-1 overflow-hidden">
        <Card className="max-w-[800px] m-auto bg-background border-none overflow-hidden py-4 my-3">
          {!data ?
            <LoadingContainer />
          : <CardContent className="flex-1 flex flex-col gap-9 mt-4">
              <div className={'flex flex-col gap-4'}>
                <div className="font-medium">VIP信息</div>
                <ul className="flex flex-col gap-3.5 p-6 bg-background rounded-lg border">
                  <Row
                    label="权益包数量"
                    value={`${data.vipInfo.interestCount}个`}
                  />
                  <Row
                    label="账号数"
                    value={`${data.vipInfo.platformAccountCount}个`}
                  />
                  <Row
                    label="成员数"
                    value={`${data.vipInfo.teamMemberCount}人`}
                  />
                  <Row
                    label="消息额度"
                    value={`${data.vipInfo.messageCount}条/天`}
                  />
                  <Row
                    label="VIP时长"
                    value={`${formatMount(data.vipInfo.month)}${data.vipInfo.freeMonth ? `（送${formatMount(data.vipInfo.freeMonth)}）` : ''}`}
                  />
                </ul>
              </div>
              <div className={'flex flex-col gap-4'}>
                <div className="font-medium">订单信息</div>
                <ul className="flex flex-col gap-3.5 p-6 bg-background rounded-lg border">
                  <Row label="订单号" value={`${data.orderInfo.orderNo}`} />
                  <Row label="团队" value={`${data.orderInfo.teamName}`} />
                  <Row
                    label="创建时间"
                    value={formatDate(data.orderInfo.fromTime)}
                  />
                  <Row
                    label="订单状态"
                    value={<OrderStatus status={data.orderInfo.orderStatus} />}
                  />
                  <Row
                    label="订单类型"
                    value={<OrderType type={data.orderInfo.type} />}
                  />
                  <Row
                    label="应付金额"
                    value={<OrderPrice price={data.orderInfo.dueAmount} />}
                  />
                  {data.orderInfo.orderStatus === 'success' && (
                    <>
                      <Row
                        label="支付金额"
                        value={<OrderPrice price={data.orderInfo.payAmount} />}
                      />
                      <Row
                        label="支付时间"
                        value={
                          data.orderInfo.payTime ?
                            dayjs(data.orderInfo.payTime).format(
                              'YYYY-MM-DD HH:mm:ss'
                            )
                          : '-'
                        }
                      />
                      <Row
                        label="支付方式"
                        value={<PayType payType={data.orderInfo.payType} />}
                      />
                    </>
                  )}
                </ul>
              </div>
            </CardContent>
          }
        </Card>
      </ScrollArea>
    </div>
  );
}
