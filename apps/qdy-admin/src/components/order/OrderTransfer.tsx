import { OrderPrice, Row } from './detail';
import { formatMount } from '@/lib/utils';
// import { formatDate } from '@/lib/utils/day';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { LoadingContainer } from '../loading';
import { Button } from '@mono/ui/button';
// import { Input } from '@mono/ui/input';
import { LoadingButton } from '../loadingButton';
// import { formatPrice } from '@/lib/utils';
import { useOrderDetail } from '@/hooks/useOrderDetail';
import { updateOrderStatus } from '@/api/order';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Form, FormField, FormItem, FormLabel } from '@mono/ui/form';
import { Textarea } from '@mono/ui/textarea';
import { useEffect } from 'react';

const formschema = z.object({
  payAmount: z.number().min(1, { message: '请输入实付金额' }),
  remark: z.string(),
});

export type TransferForm = z.infer<typeof formschema>;

// const numberTransform = {
//   input: (value: number) =>
//     isNaN(value) || value === 0 ? '' : value.toString(),
//   output: (e: React.ChangeEvent<HTMLInputElement>) => {
//     const output = parseInt(e.target.value, 10);
//     return isNaN(output) ? 0 : output;
//   },
// };

export function OrderTransfer({
  orderNo,
  onClose,
}: {
  orderNo: string;
  onClose: () => void;
}) {
  const queryClient = useQueryClient();

  const { data, isLoading } = useOrderDetail(orderNo);

  const mutation = useMutation({
    mutationFn: updateOrderStatus,
    onSuccess: () => {
      onClose();
      queryClient.refetchQueries({ queryKey: ['orderList'] });
    },
  });

  const form = useForm<TransferForm>({
    resolver: zodResolver(formschema),
    defaultValues: {
      payAmount: 0,
      remark: '',
    },
  });

  useEffect(() => {
    if (data) {
      form.setValue('payAmount', data.orderInfo.dueAmount);
    }
  }, [data]);

  if (isLoading || !data) {
    return <LoadingContainer />;
  }

  const onSubmit = (values: TransferForm) => {
    // console.log('%c 对公转账开通:\n', 'color:#FF7A45', values);
    const { payAmount, remark } = values;
    mutation.mutate({
      orderNo,
      remark,
      payAmount,
    });
  };

  return (
    <div className="flex gap-4 flex-col">
      <ul className="flex flex-col gap-2 p-4 bg-background rounded-lg border">
        <Row label="权益包数量" value={data.vipInfo.interestCount} />
        <Row
          label="开通时长"
          value={
            data.vipInfo.month === 0 ?
              `${data.orderInfo.days}天`
            : `${formatMount(data.vipInfo.month)}${data.vipInfo.freeMonth ? `（送${formatMount(data.vipInfo.freeMonth)}）` : ''}`
          }
        />
        <Row
          label="订单金额"
          value={<OrderPrice price={data.orderInfo.price} />}
        />
        <Row
          label="优惠金额"
          value={
            <OrderPrice
              price={data.orderInfo.price - data.orderInfo.dueAmount}
            />
          }
        />
        <Row
          label="应付金额"
          value={<OrderPrice price={data.orderInfo.dueAmount} />}
        />
      </ul>
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="flex flex-col gap-4"
        >
          {/* <FormField
            control={form.control}
            name="payAmount"
            render={({ field }) => (
              <FormItem className="flex items-center gap-1 space-y-0">
                <FormLabel className="w-24 flex-shrink-0">实付金额</FormLabel>
                <Input
                  type="number"
                  placeholder="请输入实付金额"
                  className="w-36"
                  {...field}
                  value={numberTransform.input(field.value)}
                  onChange={(e) => field.onChange(numberTransform.output(e))}
                />
              </FormItem>
            )}
          /> */}
          <FormField
            control={form.control}
            name="remark"
            render={({ field }) => (
              <FormItem className="flex items-center gap-1 space-y-0">
                <FormLabel className="w-24 flex-shrink-0">
                  备注（选填）
                </FormLabel>
                <Textarea
                  rows={4}
                  placeholder="请输入"
                  className="resize-none"
                  {...field}
                />
              </FormItem>
            )}
          />

          <div className="flex justify-end">
            <Button variant="outline" type="reset" onClick={onClose}>
              取消
            </Button>
            <LoadingButton
              type="submit"
              isPending={mutation.isPending}
              disabled={mutation.isPending}
              className="ml-2"
            >
              开通
            </LoadingButton>
          </div>
        </form>
      </Form>
    </div>
  );
}
