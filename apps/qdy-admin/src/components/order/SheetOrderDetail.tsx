import {
  Sheet,
  She<PERSON><PERSON>lose,
  She<PERSON><PERSON>ontent,
  She<PERSON><PERSON><PERSON><PERSON>,
  She<PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON><PERSON>er,
  Sheet<PERSON>it<PERSON>,
} from '@mono/ui/sheet';
import { VisuallyHidden } from '@radix-ui/react-visually-hidden';
import { useQuery } from '@tanstack/react-query';
import { orderDetail } from '@/api/order';
import { formatMount, formatPrice } from '@/lib/utils.ts';
import { LoadingContainer } from '@/components/loading.tsx';
import { formatDate } from '@/lib/utils/day';
import dayjs from 'dayjs';
import { Badge } from '@mono/ui/badge';
import { Order, PayType as PayTypeDto } from '@/types/order';
import { ReactNode } from 'react';
import {
  orderStatusMap,
  orderSourceMap,
  payTypeMap,
  orderTypeMap,
} from '@/config/order';
import { ScrollArea } from '@mono/ui/scroll-area';
// import { userInfo } from '@/api/user';

export function Row({ label, value }: { label: string; value: ReactNode }) {
  return (
    <li className="flex items-center gap-6 text-sm">
      <div className="w-20 text-muted-foreground">{label}:</div>
      <span className={'line-clamp-1 flex-1'}>{value}</span>
    </li>
  );
}

export function OrderStatus({
  status,
}: {
  status: 'pending' | 'success' | 'canceled' | 'refund';
}) {
  return (
    <Badge
      className={status === 'pending' ? 'bg-[#FFF1D6] text-[#B76E00]' : ''}
      variant={'secondary'}
    >
      {orderStatusMap[status]}
    </Badge>
  );
}

export function OrderType(props: { type: Order['type'] }) {
  return <span>{orderSourceMap[props.type]}</span>;
}

export function OrderPrice(props: { price: number }) {
  return <span>¥{formatPrice(props.price)}</span>;
}

export function PayType(props: { payType: PayTypeDto }) {
  return <span>{payTypeMap[props.payType] || '-'}</span>;
}

export function SheetOrderDetail({
  open,
  setOpen,
  orderNo,
}: {
  open: boolean;
  setOpen: (open: boolean) => void;
  orderNo: string;
}) {
  const { data } = useQuery({
    queryKey: ['orderDetail', orderNo],
    enabled: !!orderNo,
    queryFn: () => {
      return orderDetail(orderNo!);
    },
  });

  // const userInfoQuery = useQuery({
  //   queryKey: ['userInfo'],
  //   queryFn: userInfo,
  // });

  return (
    <Sheet open={open} onOpenChange={setOpen}>
      <SheetContent className="w-[584px] min-w-[584px] p-0 flex flex-col">
        <SheetHeader className="px-6 pt-4">
          <SheetTitle>订单详情</SheetTitle>
          <VisuallyHidden>
            <SheetDescription></SheetDescription>
          </VisuallyHidden>
        </SheetHeader>

        <div className="flex-1 w-full flex overflow-hidden">
          {!data ?
            <LoadingContainer className="w-5 h-5" />
          : <ScrollArea className="flex-1 overflow-hidden">
              <div className="flex-1 flex flex-col gap-9 px-6  pb-4">
                <div className={'flex flex-col gap-4'}>
                  <div className="font-medium">VIP信息</div>
                  <ul className="flex flex-col gap-3.5 p-6 bg-background rounded-lg border">
                    <Row
                      label="权益包数量"
                      value={`${data.vipInfo.interestCount}个`}
                    />
                    <Row
                      label="账号数"
                      value={`${data.vipInfo.platformAccountCount}个`}
                    />
                    <Row
                      label="成员数"
                      value={`${data.vipInfo.teamMemberCount}人`}
                    />
                    <Row
                      label="消息额度"
                      value={`${data.vipInfo.messageCount}条/天`}
                    />
                    <Row
                      label="VIP时长"
                      value={
                        data.vipInfo.month > 0 ?
                          `${formatMount(data.vipInfo.month)}${data.vipInfo.freeMonth ? `（送${formatMount(data.vipInfo.freeMonth)}）` : ''}`
                        : `${data.vipInfo.giftDays}天`
                      }
                    />
                  </ul>
                  {/* <ul className="flex gap-4 justify-around">
                    {orderInfoItem('权益包', `${data.vipInfo.interestCount}`)}
                    {['create', 'renew'].includes(data.orderInfo.orderType) && (
                      <>
                        {data.vipInfo.month > 0 ?
                          orderInfoItem('购买月份', `${data.vipInfo.month}`)
                        : orderInfoItem('购买天数', `${data.vipInfo.giftDays}`)}
                        {orderInfoItem('赠送月份', `${data.vipInfo.freeMonth}`)}
                      </>
                    )}
                    {data.orderInfo.orderType === 'upgrade' &&
                      orderInfoItem('购买天数', data.orderInfo.remainingDays)}
                    {data.orderInfo.orderType === 'gift' &&
                      orderInfoItem('赠送天数', data.orderInfo.giftDays)}
                  </ul> */}
                </div>

                <div className={'flex flex-col gap-4'}>
                  <div className="font-medium">订单信息</div>
                  <ul className="flex flex-col gap-3.5 p-6 bg-background rounded-lg border">
                    <Row label="订单号" value={`${data.orderInfo.orderNo}`} />
                    <Row
                      label="团队名称"
                      value={`${data.orderInfo.teamName}`}
                    />
                    <Row
                      label="团队ID"
                      value={`${data.orderInfo.invitationCode}`}
                    />
                    <Row
                      label="创建时间"
                      value={formatDate(data.orderInfo.fromTime)}
                    />
                    <Row
                      label="是否赠送"
                      value={data.orderInfo.isGiftOrder ? '是' : '否'}
                    />
                    <Row
                      label="订单状态"
                      value={
                        <OrderStatus status={data.orderInfo.orderStatus} />
                      }
                    />
                    <Row
                      label="订单类型"
                      value={orderTypeMap[data.orderInfo.orderType]}
                    />
                    <Row
                      label="订单金额"
                      value={<OrderPrice price={data.orderInfo.price} />}
                    />
                    <Row
                      label="应付金额"
                      value={<OrderPrice price={data.orderInfo.dueAmount} />}
                    />
                    <Row
                      label="优惠金额"
                      value={
                        <OrderPrice
                          price={
                            data.orderInfo.price - data.orderInfo.dueAmount
                          }
                        />
                      }
                    />
                    {data.orderInfo.orderStatus === 'success' && (
                      <>
                        <Row
                          label="支付金额"
                          value={
                            data.orderInfo.isGiftOrder ?
                              '-'
                            : <OrderPrice price={data.orderInfo.payAmount} />
                          }
                        />
                        <Row
                          label="支付时间"
                          value={
                            data.orderInfo.isGiftOrder ? '-'
                            : data.orderInfo.payTime ?
                              dayjs(data.orderInfo.payTime).format(
                                'YYYY-MM-DD HH:mm:ss'
                              )
                            : '-'
                          }
                        />
                        <Row
                          label="支付方式"
                          value={
                            data.orderInfo.isGiftOrder ?
                              '-'
                            : <PayType payType={data.orderInfo.payType} />
                          }
                        />
                      </>
                    )}
                    <Row
                      label="订单来源"
                      value={<OrderType type={data.orderInfo.type} />}
                    />

                    <Row
                      label="创建人"
                      value={`${data.orderInfo.creatorName ? data.orderInfo.creatorName : '-'}`}
                    />

                    <Row
                      label="备注"
                      value={`${data.orderInfo.remark ? data.orderInfo.remark : '-'}`}
                    />
                  </ul>
                </div>
              </div>
            </ScrollArea>
          }
        </div>

        <VisuallyHidden>
          <SheetFooter>
            <SheetClose asChild></SheetClose>
          </SheetFooter>
        </VisuallyHidden>
      </SheetContent>
    </Sheet>
  );
}

// function orderInfoItem(title: ReactNode, value: ReactNode) {
//   return (
//     <li className="flex flex-col w-40 items-center justify-between rounded-md border-2 border-muted bg-popover p-4 hover:bg-accent hover:text-accent-foreground">
//       <span className={'line-clamp-1 flex-1'}>{value}</span>
//       <div className="text-muted-foreground">{title}</div>
//     </li>
//   );
// }
