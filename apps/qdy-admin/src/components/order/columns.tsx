import { ColumnDef } from '@tanstack/react-table';
// import { formatYearMonthDay, getDuration, getTimestamp } from "@/lib/day";
// import { User } from "@/types/user";
import { formatDate } from '@/lib/utils/day';
import { Order } from '@/types/order';
import { Badge } from '@mono/ui/badge';
// import { Link } from '@tanstack/react-router';
import DropdownMenuComponent from '../DropdownMenuComponent';
import { DropdownMenuItem } from '@mono/ui/dropdown-menu';
import { Button } from '@mono/ui/button';
import { Link } from '@tanstack/react-router';
import { orderTypeMap, orderStatusMap, saleTypeMap } from '@/config/order';
import { cn } from '@/lib/utils';

export function getTableColumns(
  putStatus: (id: string) => void,
  expandDetail: (orderNo: string) => void
): Array<ColumnDef<Order>> {
  return [
    {
      header: '订单号',
      accessorKey: 'orderNumber',
      cell: ({ row }) => {
        const { orderNo } = row.original;
        return <div className="font-medium text-left">{orderNo}</div>;
      },
    },
    {
      header: '订单团队',
      accessorKey: 'team',
      cell: ({ row }) => {
        const { team } = row.original;
        return (
          <Button variant="link" className="p-0">
            <Link
              to="/team"
              search={{
                tName: team.name,
                from: 'OrderPage',
              }}
            >
              <p className="text-muted-foreground text-xs text-start">
                {team.name}
              </p>
              <p className="text-muted-foreground text-xs text-start mt-1">
                {team.invitationCode}
              </p>
            </Link>
          </Button>
        );
      },
    },
    {
      header: '手机号',
      accessorKey: 'phone',
      cell: ({ row }) => {
        const { phone } = row.original;
        return (
          <Button variant="link" className="p-0">
            <Link
              to="/team"
              search={{
                phone: phone,
                from: 'OrderPage',
              }}
            >
              {phone}
            </Link>
          </Button>
        );
      },
    },
    {
      size: 180,
      header: '创建时间',
      accessorKey: 'fromTime',
      cell: ({ row }) => {
        const { fromTime } = row.original;
        return (
          <span className="text-muted-foreground">{formatDate(fromTime)}</span>
        );
      },
    },
    {
      header: '订单类型',
      accessorKey: 'orderType',
      cell: ({ row }) => {
        const { orderType } = row.original;
        return orderType ?
            <Badge variant="outline">{orderTypeMap[orderType]}</Badge>
          : '-';
      },
    },
    {
      header: '销售类型',
      accessorKey: 'salesType',
      cell: ({ row }) => {
        const { salesType } = row.original;
        return salesType ?
            <Badge variant="outline">{saleTypeMap[salesType]}</Badge>
          : '-';
      },
    },
    {
      header: '订单状态',
      accessorKey: 'type',
      cell: ({ row }) => {
        const { orderStatus } = row.original;
        return (
          <Badge
            variant={
              orderStatus === 'pending' ? 'default'
              : orderStatus === 'success' ?
                'outline'
              : 'secondary'
            }
            className={cn(orderStatus === 'refund' ? 'text-destructive' : '')}
          >
            {orderStatusMap[orderStatus]}
          </Badge>
        );
      },
    },
    {
      header: '付款金额', // 订单金额
      accessorKey: 'payAmount',
      cell: ({ row }) => {
        const { payAmount } = row.original;
        return payAmount ? <span>¥{payAmount}</span> : '-';
      },
    },
    {
      size: 180,
      header: '支付时间',
      accessorKey: 'payTime',
      cell: ({ row }) => {
        const { payTime } = row.original;
        return payTime ?
            <span className="text-muted-foreground">{formatDate(payTime)}</span>
          : '-';
      },
    },
    {
      header: '渠道',
      accessorKey: 'channel',
      cell: ({ row }) => {
        const { channel } = row.original;
        return <span>{channel?.name || '-'}</span>;
      },
    },
    {
      header: '操作',
      accessorKey: 'id',
      cell: ({ row }) => {
        const { orderStatus } = row.original;
        return (
          <div className="flex items-center justify-center gap-1">
            <DropdownMenuComponent>
              <>
                <DropdownMenuItem
                  onClick={() => expandDetail(row.original.orderNo)}
                >
                  详情
                </DropdownMenuItem>
                {orderStatus === 'pending' && (
                  <DropdownMenuItem
                    onClick={() => putStatus(row.original.orderNo)}
                  >
                    对公转账开通
                  </DropdownMenuItem>
                )}
              </>
            </DropdownMenuComponent>
          </div>
        );
      },
    },
  ];
}
