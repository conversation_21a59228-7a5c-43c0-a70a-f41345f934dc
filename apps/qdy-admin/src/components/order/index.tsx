import { TableSearch } from '@/components/order/TableSearch';
import TableData from '@/components/table';
import { getTableColumns } from '@/components/order/columns';
import { keepPreviousData, useQuery } from '@tanstack/react-query';
import { PaginationState } from '@tanstack/react-table';
import { useMemo, useRef, useState } from 'react';
import { orderList } from '@/api/order';
import { OrderParams } from '@/types/order';
import { useSearch } from '@tanstack/react-router';
import { SheetOrderDetail } from '@/components/order/SheetOrderDetail';
import { formatPrice } from '@/lib/utils';
import { dialogBaseManager } from '../dialogBase';
import { OrderTransfer } from './OrderTransfer';

export function OrderPage() {
  const [openDetail, setOpenDetail] = useState(false);
  const [selectOrderNo, setSelectOrderNo] = useState('');
  const {
    channelId,
    orderNo,
    salesType,
    orderStartTime,
    orderEndTime,
    orderStatus,
    invitationCode,
  } = useSearch({ strict: false });
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 20,
  });
  const searchParams = useRef<OrderParams>({
    channelId: channelId ? `${channelId}` : undefined,
    orderNo: orderNo ? `${orderNo}` : undefined,
    salesType: salesType !== undefined ? salesType : 'Buy',
    orderStartTime: orderStartTime,
    orderEndTime: orderEndTime,
    orderStatus: orderStatus,
    invitationCode: invitationCode,
  });

  const query = useQuery({
    queryKey: ['orderList', pagination],
    queryFn: () =>
      orderList({
        ...searchParams?.current,
        size: pagination.pageSize,
        page: pagination.pageIndex + 1,
      }),
    placeholderData: keepPreviousData,
  });
  // const [open, setOpen] = useState(false);
  const columns = useMemo(() => {
    return getTableColumns(
      (orderNo) => {
        dialogBaseManager.open({
          title: '对公转账开通',
          render: (onClose) => (
            <OrderTransfer orderNo={orderNo} onClose={onClose} />
          ),
        });
        // alertBaseManager.open({
        //   title: '确定对公转账开通',
        //   onSubmit: () => {
        //     updateOrderStatusMutation.mutate(id);
        //   },
        // });
      },
      (orderNo: string) => {
        // 展开-查看订单详情
        // console.log(`订单号: ${orderNo}`);
        setSelectOrderNo(orderNo);
        setOpenDetail(true);
      }
    );
  }, []);

  return (
    <div className="flex flex-col w-full h-full overflow-hidden gap-4">
      <TableSearch
        values={searchParams.current}
        onSearch={(values: OrderParams) => {
          searchParams.current = values;
          setPagination({
            pageIndex: 0,
            pageSize: 20,
          });
          query.refetch();
        }}
      />
      <div>金额：¥{formatPrice(query.data?.totalAmount ?? 0)}</div>

      <TableData
        columns={columns}
        data={query.data?.data}
        rowCount={query.data?.total}
        pagination={pagination}
        setPagination={setPagination}
      />

      <SheetOrderDetail
        open={openDetail}
        setOpen={setOpenDetail}
        orderNo={selectOrderNo}
      />
    </div>
  );
}
