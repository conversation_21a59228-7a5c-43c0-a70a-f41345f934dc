import { cn } from '@/lib/utils';
import { getPlatformLogo } from '@/lib/platform';

export function PlatformBadge({
  value,
  className,
  ...props
}: { value: number } & React.HTMLAttributes<HTMLDivElement>) {
  return (
    <div
      className={cn(
        'rounded-full overflow-hidden absolute -bottom-[3px] -right-[3px] w-4 h-4',
        className
      )}
      {...props}
    >
      <img
        src={getPlatformLogo(value) ?? undefined}
        className="w-full h-full object-cover"
      />
    </div>
  );
}
