import { ColumnDef } from '@tanstack/react-table';

import { LoginRecord } from '@/types/wechat.ts';

function formatOnlineDuration(totalMinutes: string) {
  if (!totalMinutes) return '-';
  const totalMinutesNum = Number(totalMinutes);
  const hours = Math.floor(totalMinutesNum / 60);
  const minutes = totalMinutesNum % 60;
  return `${hours}小时${minutes}分钟`;
}

export function getHistoryLoginColumns(): Array<ColumnDef<LoginRecord>> {
  return [
    {
      header: '设备ID',
      accessorKey: 'appId',
      cell: ({ row }) => {
        const { appId } = row.original;
        return <span>{appId}</span>;
      },
    },
    {
      header: '最后登录时间',
      accessorKey: 'loginTime',
      cell: ({ row }) => {
        const { loginTime } = row.original;
        return <span className="text-muted-foreground">{loginTime}</span>;
      },
    },
    {
      header: '退出时间',
      accessorKey: 'logoutTime',
      cell: ({ row }) => {
        const { logoutTime } = row.original;
        return (
          <span className="text-muted-foreground">{logoutTime ?? '-'}</span>
        );
      },
    },
    {
      header: '在线时长',
      accessorKey: 'duration',
      cell: ({ row }) => {
        const { onlineDuration } = row.original;
        return <span>{formatOnlineDuration(onlineDuration)}</span>;
      },
    },
    {
      header: '登录地区',
      accessorKey: 'area',
      cell: ({ row }) => {
        const { regionId } = row.original;
        return <span>{regionId}</span>;
      },
    },
  ];
}
