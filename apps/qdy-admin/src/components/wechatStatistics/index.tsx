import { useQuery } from '@tanstack/react-query';
import { PaginationState } from '@tanstack/react-table';
import TableData from '@/components/table';
import { useMemo, useState } from 'react';
import { getTableColumns } from './columns';
import { TableSearch } from './TableSearch';
import { Wechat, WechatSearchParams } from '@/types/wechat';
import { HistoryLoginRecord } from './historyLoginRecord';
import { getOnlineCount, getWechat } from '@/api/wechat.ts';
import { TotalChart } from '@/components/wechatStatistics/totalChart.tsx';
import { DisconnectChart } from '@/components/wechatStatistics/disconnectChart.tsx';

export function WechatStatistics() {
  const [searchParams, setSearchParams] = useState<WechatSearchParams>({});

  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 30,
  });
  const [wxid, setWxid] = useState<string>('');
  const [open, setOpen] = useState(false);

  const columns = useMemo(() => {
    return getTableColumns((wechat: Wechat) => {
      setWxid(wechat.wxid);
      setOpen(true);
    });
  }, []);

  const wechatQuery = useQuery({
    queryKey: ['getWechatList', pagination, searchParams],
    queryFn: () => getWechat({ size: pagination.pageSize, page: pagination.pageIndex + 1, ...searchParams }),
  });

  const onlineCountQuery = useQuery({
    queryKey: ['getOnlineCount'],
    queryFn: () => getOnlineCount(),
  });

  return (
    <div className="flex flex-col w-full h-full overflow-hidden gap-4">
      <div className={'flex items-center gap-2 h-[200px]'}>
        <TotalChart />
        <DisconnectChart />
      </div>
      <div className="flex items-center gap-2">
        <span>当前在线列表（{onlineCountQuery.data?.onlineCount}）</span>
        <TableSearch
          onSearch={(values: WechatSearchParams) => {
            setPagination({
              pageIndex: 0,
              pageSize: 30,
            });
            setSearchParams(values);
          }}
          values={searchParams}
        />
      </div>

      <TableData
        columns={columns}
        data={wechatQuery.data?.data}
        rowCount={wechatQuery.data?.total ?? 0}
        pagination={pagination}
        setPagination={(pagination) => {
          setPagination(pagination);
        }}
      />

      <HistoryLoginRecord wxid={wxid} open={open} setOpen={setOpen} />
    </div>
  );
}
