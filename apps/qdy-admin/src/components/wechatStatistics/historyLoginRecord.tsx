import { Dialog, DialogContent, DialogTitle } from '@mono/ui/dialog';
import { useQuery } from '@tanstack/react-query';
import { PaginationState } from '@tanstack/react-table';
import { useMemo, useState } from 'react';
import TableData from '@/components/table';
import { getHistoryLoginColumns } from './historyLoginColumns';
import { getHistoryLogin } from '@/api/wechat.ts';

export function HistoryLoginRecord({
  wxid,
  open,
  setOpen,
}: {
  wxid: string;
  open: boolean;
  setOpen: (open: boolean) => void;
}) {
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 20,
  });

  const columns = useMemo(() => {
    return getHistoryLoginColumns();
  }, []);

  const historyQuery = useQuery({
    queryKey: ['getHistory', wxid],
    queryFn: () =>
      getHistoryLogin(
        {
          size: pagination.pageSize,
          page: pagination.pageIndex + 1,
        },
        wxid
      ),
  });

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent className="flex flex-col w-4/6 h-[650px] max-w-none overflow-hidden">
        <DialogTitle>历史登录信息</DialogTitle>
        <TableData
          columns={columns}
          data={historyQuery.data?.data}
          rowCount={historyQuery.data?.total ?? 0}
          pagination={pagination}
          setPagination={setPagination}
        />
      </DialogContent>
    </Dialog>
  );
}
