import { Area, AreaChart, CartesianGrid, XA<PERSON><PERSON>, YAxis } from 'recharts';
import { formatBy, formatMonthDay } from '@/lib/utils/day.ts';
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from '@mono/ui/chart';
import { getOfflineCount } from '@/api/wechat.ts';
import { useQuery } from '@tanstack/react-query';
import { format } from 'date-fns';

const chartConfig = {
  offlineCount: {
    label: '掉线数量',
    color: 'hsl(var(--chart-1))',
  },
} satisfies ChartConfig;

export function DisconnectChart() {
  const date = new Date();
  date.setDate(date.getDate() - 7);

  const { data } = useQuery({
    queryKey: ['getOfflineCount'],
    queryFn: () => getOfflineCount(format(date, 'yyyyMMdd')),
  });
  return (
    <div className={'w-full h-full pb-6'}>
      <div className={'pl-5 text-lg font-semibold'}>
        今日掉线数：{data?.offlineCount ?? '-'}
      </div>
      <ChartContainer config={chartConfig} className="h-full w-full">
        <AreaChart
          accessibilityLayer
          data={data?.statData}
          margin={{
            left: 0,
            right: 12,
            top: 10,
          }}
        >
          <CartesianGrid vertical={false} />
          <XAxis
            dataKey="offlineDate"
            tickLine={false}
            axisLine={false}
            tickMargin={0}
            tickFormatter={(value) => formatMonthDay(value)}
          />
          <YAxis width={25} axisLine={false} tick={false} />
          <ChartTooltip
            cursor={false}
            content={
              <ChartTooltipContent
                labelFormatter={(value) => formatBy(value, 'YYYY-MM-DD')}
              />
            }
          />

          <defs>
            <linearGradient id="fillofflineCount" x1="0" y1="0" x2="0" y2="1">
              <stop
                offset="5%"
                stopColor="var(--color-offlineCount)"
                stopOpacity={0.8}
              />
              <stop
                offset="95%"
                stopColor="var(--color-offlineCount)"
                stopOpacity={0.1}
              />
            </linearGradient>
          </defs>

          <Area
            dataKey="offlineCount"
            type="natural"
            fill="url(#fillofflineCount)"
            fillOpacity={0.4}
            stroke="var(--color-offlineCount)"
            stackId="offlineCountExpense"
          />
        </AreaChart>
      </ChartContainer>
    </div>
  );
}
