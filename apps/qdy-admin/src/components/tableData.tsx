import Table from '@/components/table';
import { usePageQuery } from '@/hooks/usePageQuery';
import { PageData } from '@/types';
import { ColumnDef } from '@tanstack/react-table';

type AllParams = Parameters<typeof usePageQuery>;
// 等价于 [QueryKey, (params: { size: number; page: number }) => Promise<PageData<TData>>, TParams?]

type QueryKeyType = AllParams[0]; // 第一个参数类型 // 第二个参数类型
type ParamsType = AllParams[2]; // 第三个参数类型
export function TableData<TData, TValue>({
  columns,
  queryKey,
  queryFn,
  params,
}: {
  columns: ColumnDef<TData, TValue>[];
  queryKey: QueryKeyType;
  queryFn: (params: { size: number; page: number }) => Promise<PageData<TData>>;
  params?: ParamsType;
}) {
  const { query, pagination, setPagination } = usePageQuery(
    queryKey,
    queryFn,
    params
  );
  return (
    <Table
      columns={columns}
      data={query.data?.data}
      rowCount={query.data?.total}
      pagination={pagination}
      setPagination={setPagination}
    />
  );
}
