import TableData from '@/components/table';
import { getTableColumns } from '@/components/admin/columns';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { PaginationState } from '@tanstack/react-table';
import { useMemo, useState } from 'react';
import { deleteUser, userList } from '@/api/admin';
import { CreateAdmin } from '@/components/admin/CreateAdmin';
import { Button } from '@mono/ui/button';
import { userInfo } from '@/api/user';
import { alertBaseManager } from '@/components/alertBase.tsx';
import { type Admin } from '@/types/admin';

export function Admin() {
  const queryClient = useQueryClient();
  const [open, setOpen] = useState(false);
  const [selectAdmin, setSelectAdmin] = useState<Admin | undefined>();

  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 20,
  });

  const userInfoQuery = useQuery({
    queryKey: ['userInfo'],
    queryFn: userInfo,
  });

  const query = useQuery({
    queryKey: ['userList', pagination],
    queryFn: () =>
      userList({
        size: pagination.pageSize,
        page: pagination.pageIndex + 1,
      }),
  });

  const deleteMutation = useMutation({
    mutationFn: deleteUser,
    onSuccess: () => {
      queryClient.refetchQueries({ queryKey: ['userList'] });
    },
  });

  const columns = useMemo(() => {
    return getTableColumns(
      userInfoQuery.data?.role ?? 1,
      userInfoQuery.data?.username ?? '',
      (admin: Admin) => {
        setSelectAdmin(admin);
        setOpen(true);
      },
      (adminId: string) => {
        alertBaseManager.open({
          title: '删除管理员',
          description: '确定删除该管理员吗？',
          onSubmit: () => {
            deleteMutation.mutate(adminId);
          },
        });
      }
    );
  }, [deleteMutation, userInfoQuery.data?.role]);

  return (
    <div className="flex flex-col w-full h-full overflow-hidden gap-4">
      {userInfoQuery.data?.role === 0 && (
        <Button
          variant="default"
          className="w-max"
          onClick={() => {
            setSelectAdmin(undefined);
            setOpen(true);
          }}
        >
          {'创建管理员'}
        </Button>
      )}
      <TableData
        columns={columns}
        data={query.data?.data}
        rowCount={query.data?.total ?? 0}
        pagination={pagination}
        setPagination={setPagination}
      />

      {open && (
        <CreateAdmin open={open} setOpen={setOpen} admin={selectAdmin} />
      )}
    </div>
  );
}
