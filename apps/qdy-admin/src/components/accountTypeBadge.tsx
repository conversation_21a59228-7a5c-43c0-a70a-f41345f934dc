import { cn } from '@/lib/utils';
import blueV from '@/assets/blue-v.png';

export function AccountTypeBadge({
  type,
}: {
  type: 'blue' | 'orange' | 'user';
}) {
  return (
    <div
      className={cn(
        'w-3.5 h-3.5 flex flex-shrink-0 items-center justify-center rounded-full text-white',
        type === 'blue' ? 'bg-[#1CA9F9]' : 'bg-[#FFB22C]'
      )}
    >
      <img src={blueV} className="w-3.5 h-3.5" />
    </div>
  );
}
