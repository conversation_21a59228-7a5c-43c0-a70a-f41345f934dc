import { RotateCcw } from 'lucide-react';

export function IpStatistics() {
  return (
    <div className={'flex flex-row gap-6 overflow-y-auto'}>
      <div
        className={
          'border p-4 flex flex-col gap-3 min-w-[200px] overflow-y-auto'
        }
      >
        {Array.from(Array(10).keys()).map((_, i) => (
          <div
            key={i}
            className={
              'cursor-pointer bg-gray-100 rounded-sm px-2 py-1 hover:bg-blue-100 duration-300'
            }
          >
            湖南 {i}
          </div>
        ))}
      </div>

      <div className={'p-4 flex-1'}>
        <div className={'flex flex-row justify-between'}>
          <div>总账号数：299</div>
          <div>
            <span className={'text-[14px] flex cursor-pointer select-none'}>
              <RotateCcw color={'#2660ff'} size={'18'} />
              刷新
            </span>
          </div>
        </div>
        <div className={'flex flex-wrap gap-4 mt-4'}>
          {Array.from(Array(10).keys()).map((_, i) => (
            <div className={'border rounded-md p-4 min-w-[160px]'} key={i}>
              <div>长沙：123</div>
              <div>
                状态：
                <span className={'text-green-500'}>
                  <span
                    className={
                      'w-2 h-2 inline-block bg-green-500 rounded-full mr-1'
                    }
                  ></span>
                  正常
                </span>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
