import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { Button } from '@mono/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from '@mono/ui/form';
import { Input } from '@mono/ui/input';
import { CalendarRange } from '../CalendarRange';
import { omitBy } from 'lodash';
import { ChannelParams } from '@/types/channel';

const formSchema = z.object({
  name: z.string().optional(),
  createTimeRange: z
    .object({
      from: z.date(),
      to: z.date(),
    })
    .optional(),
});

export function ChannelTableSearch({
  onSearch,
}: {
  onSearch: (values: ChannelParams) => void;
}) {
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: '',
    },
  });

  function onSubmit(values: z.infer<typeof formSchema>) {
    const params: ChannelParams = {
      name: values.name?.trim(),
      startTime: values.createTimeRange?.from?.getTime(),
      endTime: values.createTimeRange?.to?.getTime(),
    };
    const paramsOmitEmpty = omitBy(params, (value) => !value);
    onSearch(paramsOmitEmpty);
  }

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="flex flex-wrap gap-4 p-0.5"
      >
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem className="flex items-center gap-1 space-y-0">
              <FormLabel>渠道名称</FormLabel>
              <FormControl>
                <Input className="w-40" placeholder="搜索渠道名称" {...field} />
              </FormControl>
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="createTimeRange"
          render={({ field }) => (
            <FormItem className="flex items-center gap-1 space-y-0">
              <FormLabel className="">创建时间</FormLabel>
              <CalendarRange
                value={field.value}
                onChange={field.onChange}
              ></CalendarRange>
            </FormItem>
          )}
        />
        <Button type="submit">搜索</Button>
      </form>
    </Form>
  );
}
