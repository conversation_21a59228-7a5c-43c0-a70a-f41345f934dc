import { Button } from '@mono/ui/button';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>Content,
  Di<PERSON>Footer,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogDescription,
} from '@mono/ui/dialog';
import { Input } from '@mono/ui/input';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@mono/ui/form';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { createChannel, updateChannel } from '@/api/channel';
import { LoadingButton } from '@/components/loadingButton';
import { useEffect, useMemo, useRef, useState } from 'react';
import { Channel } from '@/types/channel';
import { SelectCoupon } from './SelectCoupon';
import { Eye, EyeOff } from 'lucide-react';
import { VisuallyHidden } from '@radix-ui/react-visually-hidden';

// 账号
// ^(?=.*[A-Za-z])(?=.*\d).+$
const usernameRegex = /^(?=.*[A-Za-z])(?=.*\d).+$/;
const zodValidUsername = z
  .string()
  .min(1, { message: '请输入账号' })
  .regex(usernameRegex, {
    message: '账号格式必须是：英文+数字',
  });

// 密码 满足大小写、特殊符号、数字中的2种
const passwordRegex =
  /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)|(?=.*[a-z])(?=.*[!@#$%^&*(),.?":{}|<>])(?=.*\d)|(?=.*[A-Z])(?=.*[!@#$%^&*(),.?":{}|<>])(?=.*\d)+$/;

const zodValidPasswordCreate = z
  .string()
  .min(8, { message: '请输入8-15位密码' })
  .max(15, { message: '请输入8-15位密码' })
  .regex(passwordRegex, {
    message: '密码格式不正确',
  });

const zodValidPasswordUpdate = z.string().optional();

export function Create({
  channel,
  reset,
}: {
  channel?: Channel;
  reset: () => void;
}) {
  const [open, setOpen] = useState(false);
  const type = useRef<'update' | 'add'>('add');
  const [show, setShow] = useState(false);

  const formSchema = useMemo(() => {
    return z.object({
      name: z.string().min(1, { message: '请输入渠道名称' }),
      //限英文+数字，区分大小写，6-15位数
      code: z
        .string()
        .min(6, { message: '请输入6-15位数' })
        .max(15, { message: '请输入6-15位数' })
        .regex(/^[a-zA-Z0-9]+$/, { message: '请输入英文或数字' }),
      username: zodValidUsername,
      password:
        channel && channel.ChannelAdminUser ?
          zodValidPasswordUpdate
        : zodValidPasswordCreate,
      couponId: z.string().optional(),
    });
  }, [channel]);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: '',
      code: '',
      username:
        channel && channel.ChannelAdminUser ? channel.ChannelAdminUser : '',
      password: '',
    },
  });
  const queryClient = useQueryClient();

  const editMutation = useMutation({
    mutationFn: updateChannel,
    onSuccess: () => {
      setOpen(false);
      queryClient.refetchQueries({ queryKey: ['getChannels'] });
    },
  });

  const mutation = useMutation({
    mutationFn: createChannel,
    onSuccess: () => {
      setOpen(false);
      queryClient.refetchQueries({ queryKey: ['getChannels'] });
    },
    onError: (error) => {
      console.log(error);
    },
  });

  function onSubmit(values: z.infer<typeof formSchema>) {
    const { couponId, ...rest } = values;
    if (couponId && couponId !== 'null') {
      (rest as unknown as Parameters<typeof createChannel>[0]).couponId =
        parseInt(couponId);
    }
    if (type.current === 'update') {
      const data = { ...rest, channelUserId: channel!.channelUserId };
      delete data.password;
      editMutation.mutate({ channelId: channel!.id, params: data });
    } else {
      mutation.mutate(rest);
    }
  }

  useEffect(() => {
    if (open) {
      if (type.current === 'add') {
        form.reset({
          name: '',
          code: '',
          username: '',
          password: '',
        });
        reset();
      }
    } else {
      type.current = 'add';
    }
  }, [form, open]);

  useEffect(() => {
    if (channel) {
      type.current = 'update';
      const channelInfo = { ...channel, username: '', password: '' };
      if ('couponId' in channelInfo) {
        channelInfo['couponId'] = `${channelInfo['couponId']}`;
      }
      // Object.prototype.hasOwnProperty.call(channelInfo, 'username')
      if (typeof channel.ChannelAdminUser === 'undefined') {
        channelInfo['username'] = '';
        channelInfo['password'] = '';
      } else {
        channelInfo['username'] = channel.ChannelAdminUser;
      }

      form.reset(channelInfo);
      setOpen(true);
    }
  }, [channel, form]);

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button>创建</Button>
      </DialogTrigger>
      <DialogContent
      // onInteractOutside={(e) => {
      //   e.preventDefault();
      // }}
      >
        <Form {...form}>
          <form
            className="flex flex-col gap-5"
            onSubmit={form.handleSubmit(onSubmit)}
          >
            <DialogHeader>
              <DialogTitle>
                {type.current === 'update' ? '编辑渠道' : '创建渠道'}
              </DialogTitle>

              <VisuallyHidden>
                <DialogDescription />
              </VisuallyHidden>
            </DialogHeader>
            <div className="flex flex-col gap-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <div className="flex flex-col">
                    <FormItem className="flex items-center gap-1 space-y-0">
                      <FormLabel className="w-20 flex-shrink-0">
                        渠道名称
                      </FormLabel>
                      <FormControl>
                        <Input
                          id="name"
                          placeholder="输入渠道名称"
                          {...field}
                        />
                      </FormControl>
                    </FormItem>
                    <FormMessage className="ml-20" />
                  </div>
                )}
              />

              <FormField
                control={form.control}
                name="code"
                render={({ field }) => (
                  <div className="flex flex-col">
                    <FormItem className="flex items-center gap-1 space-y-0">
                      <FormLabel className="w-20 flex-shrink-0">
                        渠道码
                      </FormLabel>
                      <FormControl>
                        <Input id="code" placeholder="输入渠道码" {...field} />
                      </FormControl>
                    </FormItem>
                    <FormMessage className="ml-20" />
                  </div>
                )}
              />

              <FormField
                control={form.control}
                name="username"
                render={({ field }) => (
                  <div className="flex flex-col">
                    <FormItem className="flex items-center gap-1 space-y-0">
                      <FormLabel className="w-20 flex-shrink-0">账号</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="请输入账号"
                          {...field}
                          autoComplete="off"
                        />
                      </FormControl>
                    </FormItem>
                    <FormMessage className="ml-20" />
                  </div>
                )}
              />

              {!channel && (
                <FormField
                  control={form.control}
                  name="password"
                  render={({ field }) => (
                    <div className="flex flex-col">
                      <FormItem className="flex items-center gap-1 space-y-0">
                        <FormLabel className="w-20 flex-shrink-0">
                          密码
                        </FormLabel>
                        <FormControl>
                          <div className="relative w-full">
                            <Input
                              placeholder="请输入密码"
                              type={show ? 'text' : 'password'}
                              {...field}
                              autoComplete="new-password"
                            />
                            <Button
                              onClick={() => setShow(!show)}
                              size="sm"
                              type="button"
                              variant="ghost"
                              className="absolute right-3 top-1/2 -translate-y-1/2"
                            >
                              {show ?
                                <Eye className="h-5 w-5" />
                              : <EyeOff className="h-5 w-5" />}
                            </Button>
                          </div>
                        </FormControl>
                      </FormItem>
                      <span className="text-[#B2B2B2] text-xs ml-20 mt-0.5">
                        密码最少8位数（8-15），满足大小写、特殊符号、数字中2种
                      </span>
                      <FormMessage className="ml-20" />
                    </div>
                  )}
                />
              )}

              <FormField
                control={form.control}
                name="couponId"
                render={({ field }) => (
                  <div className="flex flex-col">
                    <FormItem className="flex items-center gap-1 space-y-0">
                      <FormLabel className="w-20 flex-shrink-0">
                        优惠券
                      </FormLabel>
                      <FormControl>
                        <SelectCoupon {...field} />
                      </FormControl>
                    </FormItem>
                    <FormMessage className="ml-20" />
                  </div>
                )}
              />
            </div>
            <DialogFooter>
              <LoadingButton
                isPending={mutation.isPending}
                disabled={mutation.isPending}
                type="submit"
              >
                {type.current === 'update' ? '更新' : '创建'}
              </LoadingButton>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
