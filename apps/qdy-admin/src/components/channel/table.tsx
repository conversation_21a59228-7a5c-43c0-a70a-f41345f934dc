import TableData from '@/components/table';
import { usePageQuery } from '@/hooks/usePageQuery';
import { getChannels } from '@/api/channel';
import { ColumnDef } from '@tanstack/react-table';
import { Channel, ChannelParams } from '@/types/channel';

export function TableBase({
  params,
  columns,
}: {
  columns: ColumnDef<Channel, string>[];
  params?: ChannelParams;
}) {
  const { query, pagination, setPagination } = usePageQuery(
    ['getChannels'],
    getChannels,
    params
  );
  return (
    <TableData
      columns={columns}
      data={query.data?.data ?? []}
      rowCount={query.data?.total ?? 0}
      pagination={pagination}
      setPagination={setPagination}
    />
  );
}
