import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from '@mono/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from '@mono/ui/form';
import { Input } from '@mono/ui/input';
import { Button } from '@mono/ui/button';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { LoadingButton } from '@/components/loadingButton';
import { Channel } from '@/types/channel';
import { useState } from 'react';
import { Eye, EyeOff } from 'lucide-react';
import { updateChannelPassword } from '@/api/channel';
import { toast } from 'sonner';

// 密码 满足大小写、特殊符号、数字中的2种
const passwordRegex =
  /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)|(?=.*[a-z])(?=.*[!@#$%^&*(),.?":{}|<>])(?=.*\d)|(?=.*[A-Z])(?=.*[!@#$%^&*(),.?":{}|<>])(?=.*\d)+$/;

const zodValidPasswordCreate = z
  .string()
  .min(8, { message: '请输入8-15位密码' })
  .max(15, { message: '请输入8-15位密码' })
  .regex(passwordRegex, {
    message: '密码格式不正确',
  });

const formSchema = z.object({
  password: zodValidPasswordCreate,
});

export function ResetPassword({
  open,
  setOpen,
  channel,
}: {
  open: boolean;
  setOpen: (open: boolean) => void;
  channel?: Channel;
}) {
  const [show, setShow] = useState(false);
  const queryClient = useQueryClient();

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      password: '',
    },
  });

  const mutation = useMutation({
    mutationFn: updateChannelPassword,
    onSuccess: () => {
      toast.success('修改成功');
      setOpen(false);
      form.reset();
      queryClient.refetchQueries({
        queryKey: ['getChannels'],
      });
    },
  });

  const onSubmit = (values: z.infer<typeof formSchema>) => {
    // console.log("%c data:\n", "color:#FF7A45", data);
    if (channel) {
      mutation.mutate({
        id: channel.id,
        channelUserId: channel.channelUserId,
        password: values.password,
      });
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <DialogHeader>
              <DialogTitle className="text-center">重置密码</DialogTitle>
            </DialogHeader>

            <div className="flex flex-col mt-6 mb-6">
              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <div className="flex flex-col">
                    <FormItem className="flex items-center gap-1 space-y-0">
                      <FormControl>
                        <div className="relative w-full">
                          <Input
                            placeholder="请输入密码"
                            type={show ? 'text' : 'password'}
                            {...field}
                            autoComplete="new-password"
                          />
                          <Button
                            onClick={() => setShow(!show)}
                            size="sm"
                            type="button"
                            variant="ghost"
                            className="absolute right-3 top-1/2 -translate-y-1/2"
                          >
                            {show ?
                              <Eye className="h-5 w-5" />
                            : <EyeOff className="h-5 w-5" />}
                          </Button>
                        </div>
                      </FormControl>
                    </FormItem>
                    <span className="text-[#B2B2B2] text-xs mt-0.5">
                      密码最少8位数（8-15），满足大小写、特殊符号、数字中2种
                    </span>
                    <FormMessage className="ml-20" />
                  </div>
                )}
              />
            </div>

            <DialogFooter>
              <Button
                onClick={() => {
                  form.reset();
                  setOpen(false);
                }}
                variant="ghost"
                type="reset"
              >
                取消
              </Button>
              <LoadingButton
                isPending={mutation.isPending}
                disabled={mutation.isPending}
                type="submit"
              >
                确定重置
              </LoadingButton>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
