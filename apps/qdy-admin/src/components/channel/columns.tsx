import { createColumnHelper } from '@tanstack/react-table';
import { Channel } from '@/types/channel';
import { formatDate } from '@/lib/utils/day';
import DropdownMenuComponent from '../DropdownMenuComponent';
import { DropdownMenuItem } from '@mono/ui/dropdown-menu';
import { Badge } from '@mono/ui/badge';
import { formatPrice } from '@/lib/utils';
import { Switch } from '@mono/ui/switch';
import { Button } from '@mono/ui/button';
import { Link } from '@tanstack/react-router';
import { toast } from 'sonner';

const columnHelper = createColumnHelper<Channel>();

export const getColumns = (
  onChange: (
    item: Channel,
    type: 'update' | 'del' | 'status' | 'resetPassword',
    checked?: boolean
  ) => void
) => {
  return [
    columnHelper.accessor('name', {
      cell: (info) => info.getValue(),
      header: () => <span>渠道名称</span>,
    }),
    columnHelper.accessor('code', {
      cell: (info) => info.getValue(),
      header: () => <span>渠道码</span>,
    }),
    columnHelper.accessor('createTime', {
      header: () => '创建时间',
      cell: (info) => (
        <span className="text-muted-foreground">
          {formatDate(info.getValue())}
        </span>
      ),
    }),
    columnHelper.accessor('userCount', {
      cell: (info) => (
        <Button variant="link">
          <Link to="/user" search={{ channelId: info.row.original.id }}>
            {info.getValue()}
          </Link>
        </Button>
      ),
      header: () => <span>用户数</span>,
    }),
    columnHelper.accessor('orderCount', {
      cell: (info) => (
        <Button variant="link">
          <Link
            to="/order"
            search={{ channelId: info.row.original.id, orderStatus: 'success' }}
          >
            {info.getValue()}
          </Link>
        </Button>
      ),
      header: () => <span>订单数</span>,
    }),
    columnHelper.accessor('orderTotal', {
      cell: (info) => (
        <Badge variant="default">{`¥${formatPrice(info.getValue())}`}</Badge>
      ),
      header: () => <span>下单总金额</span>,
    }),
    columnHelper.accessor('status', {
      cell: (info) => (
        <Switch
          onCheckedChange={(checked) => {
            onChange(info.row.original, 'status', checked);
          }}
          checked={info.getValue()}
        />
      ),
      header: () => <span>状态</span>,
    }),
    columnHelper.accessor('id', {
      header: () => '操作',
      cell: (info) => {
        return (
          <DropdownMenuComponent>
            <DropdownMenuItem
              onClick={() => {
                navigator.clipboard.writeText(
                  `${import.meta.env.VITE_FRONTEND_URL}?channel=${info.row.original.code}`
                );
                toast.success('复制成功');
              }}
            >
              复制链接
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={() => onChange(info.row.original, 'update')}
            >
              编辑
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={() => onChange(info.row.original, 'resetPassword')}
            >
              重置密码
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={() => onChange(info.row.original, 'del')}
              className="text-destructive focus:text-destructive"
            >
              删除
            </DropdownMenuItem>
          </DropdownMenuComponent>
        );
      },
    }),
  ];
};
