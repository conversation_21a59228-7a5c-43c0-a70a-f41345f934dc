import { Button } from '@mono/ui/button';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
  DialogTrigger,
} from '@mono/ui/dialog';
import { Input } from '@mono/ui/input';
import { Textarea } from '@mono/ui/textarea';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from '@mono/ui/form';
import { Checkbox } from '@mono/ui/checkbox';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { createNotice, updateNotice } from '@/api/notice.ts';
import { LoadingButton } from '@/components/loadingButton.tsx';
import { useEffect, useRef, useState } from 'react';
import { Notify } from '@/types/notice.ts';

const formSchema = z.object({
  title: z.string().min(1, { message: '请输入标题' }),
  content: z.string().min(1, { message: '请输入内容' }),
  isToast: z.boolean(),
});

export function Create({ notice }: { notice?: Notify }) {
  const [open, setOpen] = useState(false);
  const type = useRef<'update' | 'add'>('add');
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      title: '',
      content: '',
      isToast: false,
    },
  });
  const queryClient = useQueryClient();
  const editMutation = useMutation({
    mutationFn: updateNotice,
    onSuccess: () => {
      setOpen(false);
      queryClient.refetchQueries({ queryKey: ['getNotices'] });
    },
  });

  const mutation = useMutation({
    mutationFn: createNotice,
    onSuccess: () => {
      setOpen(false);
      queryClient.refetchQueries({ queryKey: ['getNotices'] });
    },
    onError: (error) => {
      // 登录失败
      console.log(error);
    },
  });

  function onSubmit(values: z.infer<typeof formSchema>) {
    // Do something with the form values.
    console.log(values);
    if (type.current === 'update') {
      editMutation.mutate({ noticeId: notice!.id, params: values });
    } else {
      mutation.mutate(values);
    }
  }

  useEffect(() => {
    console.log(type.current);
    if (open) {
      if (type.current === 'add') {
        form.reset({
          title: '',
          content: '',
          isToast: false,
        });
      }
    } else {
      type.current = 'add';
    }
  }, [form, open]);

  useEffect(() => {
    if (notice) {
      type.current = 'update';
      form.reset(notice);
      setOpen(true);
    }
  }, [form, notice]);

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button>创建</Button>
      </DialogTrigger>
      <DialogContent className="">
        <Form {...form}>
          <form
            className="flex flex-col gap-5"
            onSubmit={form.handleSubmit(onSubmit)}
          >
            <DialogHeader>
              <DialogTitle>
                {type.current === 'update' ? '编辑通知' : '新建通知'}
              </DialogTitle>
            </DialogHeader>
            <div className="flex flex-col gap-4">
              <FormField
                control={form.control}
                name="title"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>标题</FormLabel>
                    <Input id="name" placeholder="输入标题" {...field} />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="content"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>内容</FormLabel>
                    <FormControl>
                      <Textarea
                        id="username"
                        placeholder="输入内容"
                        rows={5}
                        {...field}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="isToast"
                render={({ field }) => (
                  <FormItem className="flex items-center gap-2 space-y-0">
                    <FormLabel>是否弹窗</FormLabel>
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>
            <DialogFooter>
              <LoadingButton
                isPending={mutation.isPending}
                disabled={mutation.isPending}
                type="submit"
              >
                {type.current === 'update' ? '更新' : '创建'}
              </LoadingButton>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
