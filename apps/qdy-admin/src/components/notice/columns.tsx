import { createColumnHelper } from '@tanstack/react-table';
import { Notify } from '@/types/notice.ts';
import { formatDate } from '@/lib/utils/day.ts';
import SauryTooltip from '../tooltip';
import DropdownMenuComponent from '../DropdownMenuComponent';
import { DropdownMenuItem } from '@mono/ui/dropdown-menu';

const columnHelper = createColumnHelper<Notify>();

export const getColumns = (
  onChange: (item: Notify, type: 'update' | 'del') => void
) => {
  return [
    columnHelper.accessor('title', {
      cell: (info) => info.getValue(),
      header: () => <span>标题</span>,
    }),
    columnHelper.accessor('content', {
      cell: (info) => (
        <SauryTooltip tooltip={info.getValue()}>
          <p className="break-all whitespace-pre-wrap line-clamp-2">
            {info.getValue()}
          </p>
        </SauryTooltip>
      ),
      header: () => <span>内容</span>,
    }),
    columnHelper.accessor('createTime', {
      header: () => '创建时间',
      cell: (info) => (
        <span className="text-muted-foreground">
          {formatDate(info.getValue())}
        </span>
      ),
    }),
    columnHelper.accessor('id', {
      header: () => '操作',
      cell: (info) => {
        return (
          <DropdownMenuComponent>
            <>
              <DropdownMenuItem
                onClick={() => onChange(info.row.original, 'update')}
              >
                编辑
              </DropdownMenuItem>
              <DropdownMenuItem
                className="text-destructive focus:text-destructive"
                onClick={() => onChange(info.row.original, 'del')}
              >
                删除
              </DropdownMenuItem>
            </>
          </DropdownMenuComponent>
        );
      },
    }),
  ];
};
