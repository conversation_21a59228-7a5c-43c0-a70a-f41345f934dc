import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@mono/ui/dialog';
import {
  createRef,
  forwardRef,
  useImperativeHandle,
  useRef,
  useState,
} from 'react';

type DialogBaseHandle = {
  open: (options: {
    title: string;
    description?: string;
    className?: string;
    classNameHeader?: string;
    classNameContent?: string;
    render?: (onClose: () => void) => React.ReactNode;
  }) => void;
  close: () => void;
};

const dialogRef = createRef<DialogBaseHandle>();

export const DialogBaseWrapper = () => {
  return <DialogBase ref={dialogRef} />;
};

export const dialogBaseManager = {
  open(options: Parameters<DialogBaseHandle['open']>[0]) {
    dialogRef.current?.open(options);
  },
  close() {
    dialogRef.current?.close();
  },
};

export const DialogBase = forwardRef<DialogBaseHandle, NonNullable<unknown>>(
  (_, ref) => {
    const [open, setOpen] = useState(false);
    const options = useRef<Parameters<DialogBaseHandle['open']>[0]>();
    useImperativeHandle(ref, () => ({
      open(option) {
        options.current = option;
        setOpen(true);
      },
      close() {
        setOpen(false);
      },
    }));
    return (
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent className={options.current?.classNameContent}>
          <DialogHeader className={options.current?.classNameHeader}>
            <DialogTitle>{options.current?.title}</DialogTitle>
            {options.current?.description && (
              <DialogDescription>
                {options.current?.description}
              </DialogDescription>
            )}
          </DialogHeader>
          {options.current?.render?.(() => setOpen(false))}
        </DialogContent>
      </Dialog>
    );
  }
);
