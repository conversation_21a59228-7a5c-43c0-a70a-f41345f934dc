import { formatPrice } from '@/lib/utils';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@mono/ui/table';
import { OrderRefundItem } from '@/types/team';
import { But<PERSON> } from '@mono/ui/button';
import { Link } from '@tanstack/react-router';
import { dialogBaseManager } from '../dialogBase';

interface OrderRefundTableProps {
  data: OrderRefundItem[];
}

export function OrderRefundTable({ data }: OrderRefundTableProps) {
  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>订单号</TableHead>
          <TableHead>付款金额</TableHead>
          <TableHead>可退金额</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {data.map((item) => (
          <TableRow key={item.orderNo}>
            <TableCell className="font-medium">
              <Button onClick={dialogBaseManager.close} variant="link">
                <Link to="/order" search={{ orderNo: item.orderNo }}>
                  {item.orderNo}
                </Link>
              </Button>
            </TableCell>
            <TableCell>{formatPrice(item.actualRefundAmount)}</TableCell>
            <TableCell>{formatPrice(item.refundAmount)}</TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );
}
