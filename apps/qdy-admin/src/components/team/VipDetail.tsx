import { refund, refundOrder } from '@/api/team';
import { Row } from '../order/detail';
import { formatPrice } from '@/lib/utils';
// import { formatDate } from '@/lib/utils/day';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { LoadingContainer } from '../loading';
import { Button } from '@mono/ui/button';
import { Input } from '@mono/ui/input';
import { LoadingButton } from '../loadingButton';
// import { formatPrice } from '@/lib/utils';
import { useEffect, useMemo, useState } from 'react';
import { Textarea } from '@mono/ui/textarea';
import { OrderRefundTable } from './OrderRefundTable';

export function VipDetail({
  teamId,
  onClose,
}: {
  teamId: number;
  onClose: () => void;
}) {
  const [actualRefund, setActualRefund] = useState('');
  const [remark, setRemark] = useState('');
  const queryClient = useQueryClient();

  const { data, isFetched } = useQuery({
    queryFn: () => refundOrder(teamId),
    queryKey: ['refundOrder', teamId],
  });

  const total = useMemo(() => {
    return data?.reduce((a, b) => a + b.actualRefundAmount, 0) || 0;
  }, [data]);

  useEffect(() => {
    if (isFetched) {
      setActualRefund(total.toString());
    }
  }, [isFetched, total]);

  const mutation = useMutation({
    mutationFn: refund,
    onSuccess: () => {
      onClose();
      queryClient.refetchQueries({ queryKey: ['teamList'] });
    },
  });

  if (!data) {
    return <LoadingContainer />;
  }

  return (
    <div className="flex gap-4 flex-col">
      <div className="flex flex-col gap-4">
        <OrderRefundTable data={data} />
        <Row label="应退费" value={formatPrice(total)} />
        <Row
          label="实际退费"
          value={
            <div className="flex items-center gap-2">
              <Input
                className="w-36 focus:outline-none focus-visible:ring-0"
                type="number"
                value={actualRefund}
                onChange={(e) => setActualRefund(e.target.value)}
              />
            </div>
          }
        />
        <Row
          label="备注"
          value={
            <Textarea
              rows={4}
              placeholder="请输入（选填）"
              className="resize-none"
              value={remark}
              onChange={(e) => setRemark(e.target.value)}
            />
          }
        />
      </div>
      <div className="flex justify-end">
        <Button variant="outline" onClick={onClose}>
          取消
        </Button>
        <LoadingButton
          isPending={mutation.isPending}
          disabled={mutation.isPending}
          className="ml-2"
          onClick={() => {
            mutation.mutate({
              teamId,
              realityPrice: Number(actualRefund),
              refundAmount: total,
              remark,
            });
          }}
        >
          确定退费
        </LoadingButton>
      </div>
    </div>
  );
}
