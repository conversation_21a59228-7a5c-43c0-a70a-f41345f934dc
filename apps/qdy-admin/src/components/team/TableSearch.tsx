import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { Button } from '@mono/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from '@mono/ui/form';
import { Input } from '@mono/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@mono/ui/select';
import { CalendarRange } from '../CalendarRange';
import { TeamParams } from '@/types/team';
import { omitBy } from 'lodash';
import { saleTypeMap } from '@/config/order';
import { useEffect } from 'react';

const formSchema = z.object({
  keyword: z.string().optional(),
  phone: z.string().optional(),
  invitationCode: z.string().optional(),
  // 注册时间段
  dateRange: z
    .object({
      from: z.date(),
      to: z.date(),
    })
    .optional(),
  // vip到期时间段
  vipDateRange: z
    .object({
      from: z.date(),
      to: z.date(),
    })
    .optional(),
  // status
  status: z.enum(['all', 'vip', 'unVip']),
  salesType: z.string().optional(),
});

export function UserTableSearch({
  values,
  onSearch,
}: {
  values: TeamParams;
  onSearch: (values: TeamParams) => void;
}) {
  const { setValue, ...form } = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      status: 'all',
      keyword: values.name?.trim() ?? '',
      phone: values.phone?.trim() ?? '',
      invitationCode: values.invitationCode?.trim() ?? '',
      salesType: values.salesType ?? 'all',
    },
  });

  useEffect(() => {
    if (values?.vipExpirationStartTime && values?.vipExpirationEndTime) {
      setValue('vipDateRange', {
        from: new Date(values.vipExpirationStartTime),
        to: new Date(values.vipExpirationEndTime),
      });
    }
  }, [values]);

  function onSubmit(values: z.infer<typeof formSchema>) {
    // Do something with the form values.
    // ✅ This will be type-safe and validated.
    console.log(values);
    const params: TeamParams = {
      name: values.keyword?.trim(),
      phone: values.phone?.trim(),
      createStartTime: values.dateRange?.from?.getTime(),
      createEndTime: values.dateRange?.to?.getTime(),
      vipExpirationStartTime: values.vipDateRange?.from?.getTime(),
      vipExpirationEndTime: values.vipDateRange?.to?.getTime(),
      invitationCode: values.invitationCode?.trim(),
      isVip: values.status === 'all' ? undefined : values.status === 'vip',
      salesType: values.salesType === 'all' ? undefined : values.salesType,
    };
    // Omit empty values
    // This will remove any empty values from the object.
    // (value) => !value
    // will remove any falsy values.
    const paramsOmitEmpty = omitBy(params, (value) => {
      return value === '' || value === null || value === undefined;
    });
    console.log(paramsOmitEmpty, params);
    onSearch(paramsOmitEmpty);
  }
  return (
    <Form setValue={setValue} {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="flex flex-wrap gap-4 p-0.5"
      >
        <FormField
          control={form.control}
          name="keyword"
          render={({ field }) => (
            <FormItem className="flex items-center gap-1 space-y-0">
              <FormLabel>团队</FormLabel>
              <FormControl>
                <Input className="w-40" placeholder="搜索团队名称" {...field} />
              </FormControl>
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="invitationCode"
          render={({ field }) => (
            <FormItem className="flex items-center gap-1 space-y-0">
              <FormLabel>团队ID</FormLabel>
              <FormControl>
                <Input className="w-40" placeholder="搜索团队ID" {...field} />
              </FormControl>
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="phone"
          render={({ field }) => (
            <FormItem className="flex items-center gap-1 space-y-0">
              <FormLabel>手机号</FormLabel>
              <FormControl>
                <Input className="w-40" placeholder="搜索手机号" {...field} />
              </FormControl>
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="salesType"
          render={({ field }) => (
            <FormItem className="flex items-center gap-1 space-y-0">
              <FormLabel className="flex-shrink-0">销售类型</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="选择销售类型" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="all">全部</SelectItem>
                  {Object.keys(saleTypeMap).map((key) => {
                    return (
                      <SelectItem key={key} value={key}>
                        {saleTypeMap[key as keyof typeof saleTypeMap]}
                      </SelectItem>
                    );
                  })}
                </SelectContent>
              </Select>
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="dateRange"
          render={({ field }) => (
            <FormItem className="flex items-center gap-1 space-y-0">
              <FormLabel className="">注册时间</FormLabel>
              <CalendarRange
                value={field.value}
                onChange={field.onChange}
              ></CalendarRange>
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="status"
          render={({ field }) => (
            <FormItem className="flex items-center gap-1 space-y-0">
              <FormLabel>VIP</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="选择状态" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="all">全部</SelectItem>
                  {/* VIP */}
                  <SelectItem value="vip">已开通</SelectItem>
                  {/* 非VIP */}
                  <SelectItem value="unVip">未开通</SelectItem>
                </SelectContent>
              </Select>
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="vipDateRange"
          render={({ field }) => (
            <FormItem className="flex items-center gap-1 space-y-0">
              <FormLabel>VIP到期时间</FormLabel>
              <CalendarRange
                value={field.value}
                onChange={field.onChange}
              ></CalendarRange>
            </FormItem>
          )}
        />
        <Button type="submit">搜索</Button>
      </form>
    </Form>
  );
}
