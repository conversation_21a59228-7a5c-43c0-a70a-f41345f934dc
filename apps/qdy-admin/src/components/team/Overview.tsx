import { Dialog, DialogContent } from '@mono/ui/dialog';
import Charts, { ChartItem } from './charts';
import { formatMonthDay } from '@/lib/utils/day';
import { useQuery } from '@tanstack/react-query';
import { getTeamMessages } from '@/api/team';
import { useMemo } from 'react';

export function OverView({
  teamId,
  open,
  setOpen,
}: {
  teamId: number;
  open: boolean;
  setOpen: (open: boolean) => void;
}) {
  const { data } = useQuery({
    queryFn: () => getTeamMessages(teamId),
    queryKey: ['getTeamMessages', teamId],
    enabled: !!teamId,
  });
  const { autoList, manualList } = useMemo(() => {
    const autoList: ChartItem[] = [];
    const manualList: ChartItem[] = [];
    if (data) {
      // console.log('%c data:\n', 'color:#FF7A45', data);

      data.forEach((item) => {
        const name = formatMonthDay(item.createTime);
        autoList.push({
          name,
          single: item.autoSingleCount,
          group: item.autoGroupCount,
          comment: item.autoCommentCount,
        });
        manualList.push({
          name,
          single: item.singleCount,
          group: item.groupCount,
          comment: item.commentCount,
        });
      });
    }
    // console.log('%c autoList:\n', 'color:#FF7A45', autoList);
    // console.log('%c manualList:\n', 'color:#FF7A45', manualList);
    return { autoList, manualList };
  }, [data]);
  return (
    <Dialog open={open} onOpenChange={setOpen}>
      {/* <DialogTrigger asChild>
        <Button variant="ghost">数据</Button>;
      </DialogTrigger> */}
      <DialogContent className="w-4/6 h-[650px] max-w-none">
        <div className="px-4 flex-1 flex flex-col gap-4 overflow-hidden">
          <div className="flex-1 flex h-0 flex-col w-full border rounded-lg">
            <div className="m-4 ml-6 font-medium text-secondary-foreground">
              30日沟通消息量
            </div>
            <Charts list={manualList} />
          </div>
          <div className="flex-1 flex h-0 flex-col w-full border rounded-lg">
            <div className="m-4 ml-6 font-medium flex-shrink-0 text-secondary-foreground">
              30日自动回复消息量
            </div>
            <Charts list={autoList} />
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
