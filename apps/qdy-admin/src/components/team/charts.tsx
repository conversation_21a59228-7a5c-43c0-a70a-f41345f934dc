// import { formatMonthDay } from "@/lib/day";
// import { OverviewResponseTypes } from "@/types/overview";
// import { useMemo } from "react";
import {
  AreaChart,
  Area,
  XAxis,
  // YAxis,
  CartesianGrid,
  // Tooltip,
  // Legend,
  // ResponsiveContainer,
} from 'recharts';

import {
  ChartConfig,
  ChartContainer,
  ChartLegend,
  ChartLegendContent,
  ChartTooltip,
  ChartTooltipContent,
} from '@mono/ui/chart';

const chartConfig = {
  single: {
    label: '私信',
    color: 'hsl(var(--chart-1))',
  },
  group: {
    label: '粉丝群',
    color: 'hsl(var(--chart-2))',
  },
  comment: {
    label: '评论',
    color: 'hsl(var(--chart-3))',
  },
} satisfies ChartConfig;

export type ChartItem = {
  name: string;
  comment: number;
  single: number;
  group?: number;
};

export default function Charts({
  list,
  hiddenGroup = false,
}: {
  list: ChartItem[];
  hiddenGroup?: boolean;
}) {
  return (
    <ChartContainer
      config={chartConfig}
      className="min-h-[200px] h-full w-full"
    >
      <AreaChart data={list} accessibilityLayer>
        <XAxis dataKey="name" />
        <CartesianGrid vertical={false} />
        {/* {list.length > 0 && <Tooltip />} */}
        <ChartTooltip cursor={false} content={<ChartTooltipContent />} />
        <ChartLegend content={<ChartLegendContent />} />
        <defs>
          <linearGradient id="colorSingle" x1="0" y1="0" x2="0" y2="1">
            <stop
              offset="5%"
              stopColor="var(--color-single)"
              stopOpacity={0.8}
            />
            <stop
              offset="95%"
              stopColor="var(--color-single)"
              stopOpacity={0.1}
            />
          </linearGradient>
          {!hiddenGroup && (
            <linearGradient id="colorGroup" x1="0" y1="0" x2="0" y2="1">
              <stop
                offset="5%"
                stopColor="var(--color-group)"
                stopOpacity={0.8}
              />
              <stop
                offset="95%"
                stopColor="var(--color-group)"
                stopOpacity={0.1}
              />
            </linearGradient>
          )}
          <linearGradient id="colorComment" x1="0" y1="0" x2="0" y2="1">
            <stop
              offset="5%"
              stopColor="var(--color-comment)"
              stopOpacity={0.8}
            />
            <stop
              offset="95%"
              stopColor="var(--color-comment)"
              stopOpacity={0.1}
            />
          </linearGradient>
        </defs>

        <Area
          type="monotone"
          dataKey="single"
          stroke="var(--color-single)"
          fill="url(#colorSingle)"
        />
        {!hiddenGroup && (
          <Area
            type="monotone"
            dataKey="group"
            stroke="var(--color-group)"
            fill="url(#colorGroup)"
          />
        )}
        <Area
          type="monotone"
          dataKey="comment"
          stroke="var(--color-comment)"
          fill="url(#colorComment)"
        />
      </AreaChart>
    </ChartContainer>
  );
}
