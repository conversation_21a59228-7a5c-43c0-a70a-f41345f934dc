import { ColumnDef } from '@tanstack/react-table';
import { Team } from '@/types/team';
import { formatDate, isExpiredFun } from '@/lib/utils/day';
import { Button } from '@mono/ui/button';
import { Badge } from '@mono/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@mono/ui/avatar';
import UserBase from '../userBase';
import { ScrollArea } from '@mono/ui/scroll-area';
import {
  Dialog,
  DialogContent,
  DialogTrigger,
  DialogTitle,
  DialogDescription,
} from '@mono/ui/dialog';
import DropdownMenuComponent from '../DropdownMenuComponent.tsx';
import { DropdownMenuItem } from '@mono/ui/dropdown-menu';
import { dialogBaseManager } from '../dialogBase.tsx';
import { VipDetail } from './VipDetail.tsx';
import { openPlatform } from '@/lib/utils/use-open.ts';
import { VisuallyHidden } from '@radix-ui/react-visually-hidden';
import { RefundLog } from './RefundLog.tsx';
import {
  douyinPlatform,
  kuaishouPlatform,
  wechatPlatform,
  weiboPlatform,
  xiaohongshuPlatform,
} from '@/lib/platform.ts';
import { saleTypeMap } from '@/config/order.ts';
import { MoveDown, MoveUp } from 'lucide-react';
import { cn } from '@/lib/utils.ts';
import { Link } from '@tanstack/react-router';

export function getTableColumns(
  handlerOverview: (id: number) => void,
  sortByPlatformAccounts?: string | null,
  sortByMembers?: string | null,
  onSortByPlatformAccounts?: (sort: string) => void,
  onSortByMembers?: (sort: string) => void
  // onViewFinancialRecords: (id: number) => void
): Array<ColumnDef<Team>> {
  return [
    {
      header: '团队',
      accessorKey: 'teamName',
      size: 250,
      cell: ({ row }) => {
        const { name, avatar, invitationCode } = row.original;
        return (
          <div className="flex">
            <Avatar>
              <AvatarImage src={avatar} alt="@shadcn" />
              <AvatarFallback>
                {name.toUpperCase().substring(0, 2)}
              </AvatarFallback>
            </Avatar>
            <div className="flex flex-col ml-2 items-start gap-1 justify-center">
              <p className="font-medium line-clamp-1">{name}</p>
              <p className="text-muted-foreground text-xs">{invitationCode}</p>
            </div>
          </div>
        );
      },
    },
    // 创建人手机号
    {
      header: '创建人手机号',
      accessorKey: 'owner.phone',
      cell: ({ row }) => {
        const { owner } = row.original;
        return <span className="text-muted-foreground">{owner.phone}</span>;
      },
    },
    {
      header: '注册时间',
      accessorKey: 'registerTime',
      cell: ({ row }) => {
        const { createTime } = row.original;
        return (
          <span className="text-muted-foreground">
            {formatDate(createTime)}
          </span>
        );
      },
    },
    {
      header: () => {
        return (
          <div
            className="flex items-center gap-1 cursor-pointer"
            onClick={() => {
              onSortByPlatformAccounts?.(
                (
                  !sortByPlatformAccounts ||
                    sortByPlatformAccounts === 'platformAccounts.asc'
                ) ?
                  'platformAccounts.desc'
                : 'platformAccounts.asc'
              );
            }}
          >
            授权账号
            <div className="flex -space-x-2">
              <MoveDown
                className={cn(
                  'w-4 h-4',
                  sortByPlatformAccounts === 'platformAccounts.desc' ?
                    'opacity-100'
                  : 'opacity-40'
                )}
              />
              <MoveUp
                className={cn(
                  'w-4 h-4',
                  sortByPlatformAccounts === 'platformAccounts.asc' ?
                    'opacity-100'
                  : 'opacity-40'
                )}
              />
            </div>
          </div>
        );
      },
      accessorKey: 'teamCount',
      cell: ({ row }) => {
        const { vip, platformAccountCount, platformAccounts } = row.original;
        // 抖音
        const douyinPlatformAccounts = platformAccounts?.filter((x) =>
          douyinPlatform(x.platform)
        );
        // 视频号
        const weixinPlatformAccounts = platformAccounts?.filter((x) =>
          wechatPlatform(x.platform)
        );
        // 微博
        const weiboPlatformAccounts = platformAccounts?.filter((x) =>
          weiboPlatform(x.platform)
        );
        // 快手
        const kuaishouPlatformAccounts = platformAccounts?.filter((x) =>
          kuaishouPlatform(x.platform)
        );
        // 小红书
        const xiaohongshuPlatformAccounts = platformAccounts?.filter((x) =>
          xiaohongshuPlatform(x.platform)
        );

        return (
          <Badge variant="secondary">
            {platformAccountCount > 0 ?
              <Dialog>
                <DialogTrigger asChild>
                  <Button variant="link" className="px-0 h-4 text-xs">
                    {platformAccountCount}
                    <span className="text-muted-foreground">
                      /{vip ? vip.platformAccountNumberLimit : '-'}
                    </span>
                  </Button>
                </DialogTrigger>

                <DialogContent className="!max-w-none w-[844px] p-0">
                  <DialogTitle className="text-lg leading-6 font-medium pl-6 pt-[22px]">
                    查看授权账号
                  </DialogTitle>
                  <VisuallyHidden>
                    <DialogDescription />
                  </VisuallyHidden>
                  <ScrollArea
                    style={{
                      // maxHeight: 'calc(100vh - 80px)',
                      maxHeight: '490px',
                    }}
                    className="overflow-hidden"
                  >
                    <div className="flex flex-col">
                      {douyinPlatformAccounts?.length > 0 && (
                        <div>
                          <p className="px-6 mb-3">抖音</p>
                          <div className="grid grid-cols-3 gap-4 px-6 pb-4">
                            {douyinPlatformAccounts.map((item) => (
                              <div
                                className="px-4 py-[14px] bg-[#FAFAFA] border border-solid border-[#F1F1F1] rounded-lg"
                                key={item.id}
                              >
                                <UserBase
                                  size="lg"
                                  {...item}
                                  onItemClick={(
                                    platformId: number,
                                    name: string
                                  ) => openPlatform(platformId, name)}
                                ></UserBase>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      {weixinPlatformAccounts?.length > 0 && (
                        <div>
                          <p className="px-6 mb-3">视频号</p>
                          <div className="grid grid-cols-3 gap-4 px-6 pb-4">
                            {weixinPlatformAccounts.map((item) => (
                              <div
                                className="px-4 py-[14px] bg-[#FAFAFA] border border-solid border-[#F1F1F1] rounded-lg"
                                key={item.id}
                              >
                                <UserBase size="lg" {...item}></UserBase>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      {xiaohongshuPlatformAccounts?.length > 0 && (
                        <div>
                          <p className="px-6 mb-3">小红书</p>
                          <div className="grid grid-cols-3 gap-4 px-6 pb-4">
                            {xiaohongshuPlatformAccounts.map((item) => (
                              <div
                                className="px-4 py-[14px] bg-[#FAFAFA] border border-solid border-[#F1F1F1] rounded-lg"
                                key={item.id}
                              >
                                <UserBase size="lg" {...item}></UserBase>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      {kuaishouPlatformAccounts?.length > 0 && (
                        <div>
                          <p className="px-6 mb-3">快手</p>
                          <div className="grid grid-cols-3 gap-4 px-6 pb-4">
                            {kuaishouPlatformAccounts.map((item) => (
                              <div
                                className="px-4 py-[14px] bg-[#FAFAFA] border border-solid border-[#F1F1F1] rounded-lg"
                                key={item.id}
                              >
                                <UserBase size="lg" {...item}></UserBase>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      {weiboPlatformAccounts?.length > 0 && (
                        <div>
                          <p className="px-6 mb-3">微博</p>
                          <div className="grid grid-cols-3 gap-4 px-6 pb-4">
                            {weiboPlatformAccounts.map((item) => (
                              <div
                                className="px-4 py-[14px] bg-[#FAFAFA] border border-solid border-[#F1F1F1] rounded-lg"
                                key={item.id}
                              >
                                <UserBase size="lg" {...item}></UserBase>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </ScrollArea>
                </DialogContent>
              </Dialog>
            : <>
                {platformAccountCount}
                <span className="text-muted-foreground">
                  /{vip ? vip.platformAccountNumberLimit : '-'}
                </span>
              </>
            }
          </Badge>
        );
      },
    },
    {
      header: () => {
        return (
          <div
            className="flex items-center gap-1 cursor-pointer"
            onClick={() => {
              onSortByMembers?.(
                !sortByMembers || sortByMembers === 'members.asc' ?
                  'members.desc'
                : 'members.asc'
              );
            }}
          >
            团队成员
            <div className="flex -space-x-2">
              <MoveDown
                className={cn(
                  'w-4 h-4',
                  sortByMembers === 'members.desc' ? 'opacity-100' : (
                    'opacity-40'
                  )
                )}
              />
              <MoveUp
                className={cn(
                  'w-4 h-4',
                  sortByMembers === 'members.asc' ? 'opacity-100' : 'opacity-40'
                )}
              />
            </div>
          </div>
        );
      },
      accessorKey: 'memberCount',
      cell: ({ row }) => {
        const { vip, members } = row.original;
        return (
          <Badge variant="secondary">
            {members}
            <span className="text-muted-foreground">
              /{vip ? vip.teamMemberNumberLimit : '-'}
            </span>
          </Badge>
        );
      },
    },
    {
      header: '销售类型',
      accessorKey: 'salesType',
      cell: ({ row }) => {
        const { salesType } = row.original;
        return salesType ?
            <Badge variant="outline">{saleTypeMap[salesType]}</Badge>
          : '-';
      },
    },
    {
      header: '订单数',
      accessorKey: 'orderNum',
      cell: ({ row }) => {
        const { invitationCode, orders } = row.original;
        return invitationCode ?
            <Button variant="link" className="p-0">
              <Link
                to="/order"
                search={{
                  invitationCode: invitationCode,
                  orderStatus: 'success',
                }}
              >
                {orders}
              </Link>
            </Button>
          : '-';
      },
    },
    {
      header: 'VIP',
      accessorKey: 'isVip',
      cell: ({ row }) => {
        const { vip } = row.original;
        const isVip = vip ? !isExpiredFun(vip.expirationTime) : false;
        return (
          // <Badge variant={isVip ? 'default' : 'outline'}>
          //   {isVip ? '是' : '否'}
          // </Badge>
          <span>{isVip ? '已开通' : '未开通'}</span>
        );
      },
    },
    // {
    //   header: "付款金额",
    //   accessorKey: "payAmount",
    //   cell: ({ row }) => {
    //     const { vip } = row.original;
    //     return (
    //       <div className="flex font-medium items-center gap-2">
    //         <span>{vip ? vip.fansGroupManageLimit : 0}</span>
    //       </div>
    //     );
    //   },
    // },
    {
      header: 'VIP购买时间',
      accessorKey: 'vipBuyTime',
      cell: ({ row }) => {
        const { vip } = row.original;
        return (
          <span className="text-muted-foreground">
            {vip && vip.createTime !== 0 ? formatDate(vip.createTime) : '-'}
          </span>
        );
      },
    },
    {
      header: 'VIP到期时间',
      accessorKey: 'vipExpireTime',
      cell: ({ row }) => {
        const { vip } = row.original;
        return (
          <span className="text-muted-foreground">
            {vip && vip.expirationTime !== 0 ?
              formatDate(vip.expirationTime)
            : '-'}
          </span>
        );
      },
    },
    {
      header: '',
      accessorKey: 'action',
      cell: ({ row }) => {
        const { id, vip, hasRefund, Refund } = row.original;
        const isVip = vip ? !isExpiredFun(vip.expirationTime) : false;
        return (
          <DropdownMenuComponent>
            <>
              <DropdownMenuItem onClick={() => handlerOverview(id)}>
                详情
              </DropdownMenuItem>
              {isVip && (
                <DropdownMenuItem
                  className="text-destructive focus:text-destructive"
                  onClick={() => {
                    dialogBaseManager.open({
                      title: '退费',
                      className: 'w-[700px] !max-w-none',
                      description:
                        '退费后该团队 VIP权益将清空，变为非VIP，对应团队里的账号、人员冻结',
                      render: (onClose) => (
                        <VipDetail teamId={id} onClose={onClose} />
                      ),
                    });
                  }}
                >
                  退费
                </DropdownMenuItem>
              )}
              {hasRefund && (
                <DropdownMenuItem
                  onClick={() => {
                    dialogBaseManager.open({
                      title: '退费记录',
                      classNameHeader: 'px-6',
                      classNameContent:
                        'w-[700px] !max-w-none overflow-hidden px-0',
                      render: () => <RefundLog data={Refund!} />,
                    });
                  }}
                >
                  退费记录
                </DropdownMenuItem>
              )}

              {/* <DropdownMenuItem
                onClick={() => {
                  onViewFinancialRecords(id);
                }}
              >
                资金变动记录
              </DropdownMenuItem> */}
            </>
          </DropdownMenuComponent>
        );
      },
    },
  ];
}
