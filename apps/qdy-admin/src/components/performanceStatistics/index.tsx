import { getTeamRate } from '@/api/performance';
import { PerformanceSearchParams } from '@/types/performance';
import { Card } from '@mono/ui/card';
import { useQuery } from '@tanstack/react-query';
import { PaginationState } from '@tanstack/react-table';
import TableData from '@/components/table';
import { useMemo, useState } from 'react';
import { getColumns } from './columns';
import { TableSearch } from './TableSearch';

export function PerformanceStatistics() {
  const [searchParams, setSearchParams] = useState<PerformanceSearchParams>({});
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 20,
  });

  const columns = useMemo(() => {
    return getColumns();
  }, []);

  const performanceQuery = useQuery({
    queryKey: ['getTeamRate', pagination, searchParams],
    queryFn: () =>
      getTeamRate({
        size: pagination.pageSize,
        page: pagination.pageIndex + 1,
        ...searchParams,
      }),
  });

  // 汇总业绩统计
  const summaryData = useMemo(() => {
    if (performanceQuery.data) {
      return [
        {
          name: '注册有效团队数',
          count: performanceQuery.data.registerTeamTotal,
        },
        {
          name: '过期未续费团队数',
          count: performanceQuery.data.expiredTeamTotal,
        },
        {
          name: '新购团队数',
          count: performanceQuery.data.paidTeamTotal,
        },
        {
          name: '复购团队数',
          count: performanceQuery.data.renewTeamTotal,
        },
        {
          name: '转化率',
          count: performanceQuery.data.conversionRateTotal,
          rate: true,
        },
        {
          name: '续费率',
          count: performanceQuery.data.renewRateTotal,
          rate: true,
        },
      ];
    }
  }, [performanceQuery.data]);

  return (
    <div className="flex flex-col w-full h-full overflow-hidden gap-4">
      <div className="flex items-center gap-2">
        <TableSearch
          onSearch={(values: PerformanceSearchParams) => {
            setSearchParams(values);
            setPagination({
              pageIndex: 0,
              pageSize: 20,
            });
          }}
          values={searchParams}
        />
      </div>
      <Card className="grid h-20 grid-cols-6 my-1 grid-rows-1 gap-4">
        {summaryData?.map((item) => (
          <div
            key={item.name}
            className="flex flex-col items-center justify-center"
          >
            <span className="text-2xl font-bold">
              {item.count}
              {item.rate && '%'}
            </span>
            <span className="text-sm">{item.name}</span>
          </div>
        ))}
      </Card>

      <TableData
        columns={columns}
        data={performanceQuery.data?.data}
        rowCount={performanceQuery.data?.total ?? 0}
        pagination={pagination}
        setPagination={setPagination}
      />
    </div>
  );
}
