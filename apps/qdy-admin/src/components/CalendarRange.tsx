import { Button } from '@mono/ui/button';
import { FormControl } from '@mono/ui/form';
import { Popover, PopoverContent, PopoverTrigger } from '@mono/ui/popover';
import { cn } from '@/lib/utils';
import { CalendarIcon } from 'lucide-react';
import { formatYearMonthDay } from '@/lib/utils/day';
import { Calendar } from '@mono/ui/calendar';
import { zhCN } from 'date-fns/locale';

export function CalendarRange({
  value,
  onChange,
}: {
  value?: { from: Date; to: Date };
  onChange?: (value?: { from?: Date; to?: Date }) => void;
}) {
  return (
    <Popover>
      <PopoverTrigger asChild>
        <FormControl>
          <Button
            variant={'outline'}
            className={cn(
              'w-[220px] pl-3 text-left font-normal',
              !value && 'text-muted-foreground'
            )}
          >
            {value ?
              value.to ?
                <>
                  {formatYearMonthDay(value.from)} -{' '}
                  {formatYearMonthDay(value.to)}
                </>
              : formatYearMonthDay(value.from)
            : <span>选择时间段</span>}
            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
          </Button>
        </FormControl>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0" align="start">
        <Calendar
          locale={zhCN}
          mode="range"
          selected={value}
          onSelect={onChange}
          numberOfMonths={2}
          initialFocus
        />
      </PopoverContent>
    </Popover>
  );
}
