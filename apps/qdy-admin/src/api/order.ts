import {
  Order,
  OrderCreateDTO,
  OrderDetail,
  OrderParams,
  OrderPriceReq,
  VipData,
} from '@/types/order';
import { makeRequest } from '.';
import { PageData, PageQuery } from '@/types';
import { TransferForm } from '@/components/order/OrderTransfer';
// 创建 订单
// POST /orders
// 接口ID：184921611
// 接口地址：https://app.apifox.com/project/4645177/apis/api-184921611
export const createOrder = (params: OrderCreateDTO) => {
  return makeRequest({
    url: '/orders',
    method: 'POST',
    data: params,
  });
};

// 升级/续费订单
//   POST /orders/update
//   接口ID：215960816
//   接口地址：https://app.apifox.com/link/project/4645177/apis/api-215960816
export const updateOrder = (params: OrderCreateDTO) => {
  return makeRequest({
    url: '/orders/update',
    method: 'POST',
    data: params,
  });
};

// 获取 订单列表
// GET /orders
// 接口ID：184921613
// 接口地址：https://app.apifox.com/project/4645177/apis/api-184921613
export const orderList = (params: OrderParams & PageQuery) => {
  return makeRequest<PageData<Order> & { totalAmount: number }>({
    url: '/orders',
    method: 'GET',
    params,
  });
};

// 对公转账开通
// PATCH /orders/{orderNo}/status
// 接口ID：210871438
// 接口地址：https://app.apifox.com/link/project/4645177/apis/api-210871438
export const updateOrderStatus = ({
  orderNo,
  ...params
}: {
  orderNo: string;
} & TransferForm) => {
  return makeRequest({
    url: `/orders/${orderNo}/status`,
    method: 'PATCH',
    data: params,
  });
};

// 获取订单VIP规格
//   GET /orders/interest
//   接口ID：213445247
//   接口地址：https://app.apifox.com/link/project/4645177/apis/api-213445247
export const getInterest = () => {
  return makeRequest<VipData>({
    url: '/orders/interest',
    method: 'GET',
  });
};

// 订单详情
//   GET /orders/{orderNo}
//   接口ID：213404536
//   接口地址：https://app.apifox.com/link/project/4645177/apis/api-213404536
export const orderDetail = (orderNo: string) => {
  return makeRequest<OrderDetail>({
    url: `/orders/${orderNo}`,
    method: 'GET',
  });
};

// 订单计算金额
//   POST /orders/price
//   接口ID：238167051
//   接口地址：https://app.apifox.com/link/project/4645177/apis/api-238167051
export const orderPrice = (params: OrderPriceReq) => {
  return makeRequest<{
    orderAmount: number;
    discountAmount: number;
    couponAmount: number;
    tips: string;
    tipsCn: string;
  }>({
    url: '/orders/price',
    method: 'POST',
    data: params,
  });
};

// 升级订单
//   POST /orders/upgrade
//   接口ID：238114090
//   接口地址：https://app.apifox.com/link/project/4645177/apis/api-238114090
export const upgradeOrder = (params: {
  interestCount: number;
  interestId: number;
  isPay: boolean;
  payAmount?: number;
  remark?: string;
  teamId: number;
}) => {
  return makeRequest({
    url: '/orders/upgrade',
    method: 'POST',
    data: params,
  });
};

// 续费vip
//   POST /orders/renew
//   接口ID：238114091
//   接口地址：https://app.apifox.com/link/project/4645177/apis/api-238114091
export const renewOrder = (params: {
  interestId: number;
  isPay: boolean;
  month: number;
  payAmount: number;
  remark?: string;
  teamId: number;
  days: number;
}) => {
  return makeRequest({
    url: '/orders/renew',
    method: 'POST',
    data: params,
  });
};

// 赠送VIP
//   POST /orders/gift
//   接口ID：242715185
//   接口地址：https://app.apifox.com/link/project/4645177/apis/api-242715185
export const giftOrder = (params: {
  giftDays: number;
  interestId: number;
  remark?: string;
  teamId: number;
}) => {
  return makeRequest({
    url: '/orders/gift',
    method: 'POST',
    data: params,
  });
};
