// 创建团队
// POST /teams
// 接口ID：166689746

import {
  OverviewResponseTypes,
  Team,
  TeamBase,
  TeamOrderStatus,
  TeamParams,
  VipCreateDTO,
  OrderRefundItem,
} from '@/types/team';
import { makeRequest } from '.';
import { PageData, PageQuery } from '@/types';

// 获取团队列表
// GET /teams
// 接口ID：183260501
// 接口地址：https://www.apifox.cn/project/4645177/apis/api-183260501
export const teamList = (params: PageQuery & TeamParams) => {
  return makeRequest<PageData<Team>>({
    url: '/teams',
    method: 'GET',
    params,
  });
};

// 创建 VIP
// POST /vip
// 接口ID：183259869
// 接口地址：https://app.apifox.com/project/4645177/apis/api-183259869

export const createVip = (params: VipCreateDTO) => {
  return makeRequest<Team>({
    url: '/vip',
    method: 'POST',
    data: params,
  });
};

// 更新 VIP
// PUT /vip
// 接口ID：183259870
// 接口地址：https://app.apifox.com/project/4645177/apis/api-183259870

export const updateVip = (params: VipCreateDTO & { id?: number }) => {
  return makeRequest<Team>({
    url: '/vip',
    method: 'PUT',
    data: params,
  });
};

// 根据手机号获取团队
// GET /teams/{phone}/team
// 接口ID：184966852
// 接口地址：https://app.apifox.com/project/4645177/apis/api-184966852

export const getTeamByPhone = (phone: string) => {
  return makeRequest<TeamBase[]>({
    url: `/teams/${phone}/team`,
    method: 'GET',
  });
};

// 获取团队消息
// GET /teams/{teamId}/messages
// 接口ID：189672597
// 接口地址：https://app.apifox.com/project/4645177/apis/api-189672597

export const getTeamMessages = (teamId: number) => {
  return makeRequest<OverviewResponseTypes[]>({
    url: `/teams/${teamId}/messages`,
    method: 'GET',
  });
};

// 获取团队订单
// GET /teams/{teamId}/order
// 接口ID：213363881
// 接口地址：https://app.apifox.com/link/project/4645177/apis/api-213363881

export const getTeamOrder = (teamId: string) => {
  return makeRequest<TeamOrderStatus>({
    url: `/teams/${teamId}/order`,
    method: 'GET',
  });
};

// 退款对应订单
//   POST /orders/{teamId}/refund/orders
//   接口ID：238114092
//   接口地址：https://app.apifox.com/link/project/4645177/apis/api-238114092
export const refundOrder = (teamId: number) => {
  return makeRequest<OrderRefundItem[]>({
    url: `/teams/${teamId}/refund/orders`,
    method: 'POST',
  });
};
// 退款
//   POST /orders/{teamId}/refund
//   接口ID：218058979
//   接口地址：https://app.apifox.com/link/project/4645177/apis/api-218058979
export const refund = ({
  teamId,
  ...data
}: {
  teamId: number;
  realityPrice: number;
  refundAmount: number;
  remark: string;
}) => {
  return makeRequest({
    url: `/teams/${teamId}/refund`,
    method: 'POST',
    data,
  });
};

// 获取团队资金变化记录
// GET /teams/{teamId}/orderRecord
// 接口ID：220997805
// 接口地址：https://app.apifox.com/link/project/4645177/apis/api-220997805
export const orderRecordList = (teamId: number) => {
  return makeRequest<{ id: number; type: string; price: number }[]>({
    url: `/teams/${teamId}/orderRecord`,
    method: 'GET',
  });
};
