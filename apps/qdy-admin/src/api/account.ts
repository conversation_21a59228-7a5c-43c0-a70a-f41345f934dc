import { makeRequest } from '.';
import { PageData, PageQuery } from '@/types';
import { AccountParams, Account } from '@/types/account';

// 获取账号列表
// GET /account
// 接口ID：*********
// 接口地址：https://app.apifox.com/link/project/4645177/apis/api-*********
export const accountList = (params: AccountParams & PageQuery) => {
  return makeRequest<PageData<Account>>({
    url: '/account',
    method: 'GET',
    params,
  });
};

// 解除账号
// POST /account/{platformAccountId}/unAuthorize
// 接口ID：*********
// 接口地址：https://app.apifox.com/link/project/4645177/apis/api-*********
export const unAuthorize = (platformAccountId: number) => {
  return makeRequest({
    url: `/account/${platformAccountId}/unAuthorize`,
    method: 'POST',
  });
};
