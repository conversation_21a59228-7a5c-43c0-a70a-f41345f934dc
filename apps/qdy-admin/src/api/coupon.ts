import { makeRequest } from '.';
import { PageData, PageQuery } from '@/types';
import {
  Coupon,
  CouponBase,
  ActivationRecordParams,
  ActivationRecord,
} from '@/types/coupon';

// 获取优惠券列表
//   GET /coupons
//   接口ID：213582675
//   接口地址：https://app.apifox.com/link/project/4645177/apis/api-213582675
export const getCoupons = (params: PageQuery) => {
  return makeRequest<PageData<Coupon>>({
    url: '/coupons',
    method: 'GET',
    params,
  });
};

// 创建优惠券
// POST /coupons
// 接口ID：213582676
// 接口地址：https://app.apifox.com/link/project/4645177/apis/api-213582676
export const createCoupon = (params: CouponBase) => {
  return makeRequest<Coupon>({
    url: '/coupons',
    method: 'POST',
    data: params,
  });
};

// 更新优惠券
//   PATCH /coupons
//   接口ID：214433732
//   接口地址：https://app.apifox.com/link/project/4645177/apis/api-214433732
export const updateCoupon = (params: CouponBase) => {
  return makeRequest<Coupon>({
    url: '/coupons',
    method: 'PATCH',
    data: params,
  });
};

// 删除优惠券
// DELETE /coupons/{couponId}
// 接口ID：213582678
// 接口地址：https://app.apifox.com/link/project/4645177/apis/api-213582678
export const deleteCoupon = (couponId: string) => {
  return makeRequest({
    url: `/coupons/${couponId}`,
    method: 'DELETE',
  });
};

// 发放优惠券
//   POST /coupons/send
//   接口ID：213582676
//   接口地址：https://app.apifox.com/link/project/4645177/apis/api-213582676
export const sendCoupon = (params: { couponsId: number; phone: string }) => {
  return makeRequest({
    url: '/coupons/send',
    method: 'POST',
    data: params,
  });
};

// 获取优惠券
//   GET /coupons/{couponsId}
//   接口ID：218058980
//   接口地址：https://app.apifox.com/link/project/4645177/apis/api-218058980
export const getCoupon = (couponsId: string) => {
  return makeRequest<Coupon>({
    url: `/coupons/${couponsId}`,
    method: 'GET',
  });
};

// 获取优惠券发放记录
// GET /coupons/send-record
// 接口ID：219187864
// 接口地址：https://app.apifox.com/link/project/4645177/apis/api-219187864
export const sendRecordList = (params: ActivationRecordParams & PageQuery) => {
  return makeRequest<PageData<ActivationRecord> & { totalAmount: number }>({
    url: '/coupons/send-record',
    method: 'GET',
    params,
  });
};
