import { makeRequest } from '.';
import { PageData, PageQuery } from '@/types';
import { Notify, NotifyBase } from '@/types/notice.ts';

// 获取系统公告
// GET /public/notices
// 接口ID：213414402
// 接口地址：https://app.apifox.com/link/project/4645177/apis/api-213414402
export const getNotices = (params: PageQuery) => {
  return makeRequest<PageData<Notify>>({
    url: '/public/notices',
    method: 'GET',
    params,
  });
};

// 创建系统公告
// POST /public/notices
// 接口ID：213414403
// 接口地址：https://app.apifox.com/link/project/4645177/apis/api-213414403
export const createNotice = (params: NotifyBase) => {
  return makeRequest<Notify>({
    url: '/public/notices',
    method: 'POST',
    data: params,
  });
};

// 更新系统公告
// PUT /public/notices/{noticeId}
// 接口ID：213414404
// 接口地址：https://app.apifox.com/link/project/4645177/apis/api-213414404
export const updateNotice = ({
  noticeId,
  params,
}: {
  noticeId: string;
  params: NotifyBase;
}) => {
  return makeRequest<Notify>({
    url: `/public/notices/${noticeId}`,
    method: 'PUT',
    data: params,
  });
};

// 删除系统公告
// DELETE /public/notices/{noticeId}
// 接口ID：213414405
// 接口地址：https://app.apifox.com/link/project/4645177/apis/api-213414405
export const deleteNotice = (noticeId: string) => {
  return makeRequest({
    url: `/public/notices/${noticeId}`,
    method: 'DELETE',
  });
};
