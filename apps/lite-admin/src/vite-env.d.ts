/// <reference types="vite/client" />

interface ImportMetaEnv {
  // API地址
  readonly VITE_APP_API_URL: string;
  readonly VITE_OPENID_ISSUER_URL: string;
  readonly VITE_OPENID_CLIENT_ID: string;
  readonly VITE_OPENID_CLIENT_SECRET: string;
  readonly VITE_OPENID_REDIRECT_URL: string;
  readonly VITE_APP_NODE_ENV: string;
  readonly VITE_APP_OFFICIAL_URL: string;
}

interface ImportMeta {
  readonly env: ImportMetaEnv;
}
