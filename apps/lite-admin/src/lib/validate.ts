import { z } from 'zod';
const passwordRegex =
  /^(?:(?=.*[a-z])(?=.*[A-Z])|(?=.*[a-z])(?=.*\d)|(?=.*[a-z])(?=.*[\W_])|(?=.*[A-Z])(?=.*\d)|(?=.*[A-Z])(?=.*[\W_])|(?=.*\d)(?=.*[\W_])).+$/;

export const zodIsPassword = z
  .string()
  .min(1, { message: '密码不能为空' })
  .min(8, { message: '请输入8-16位密码' })
  .max(16, { message: '请输入8-16位密码' })
  .regex(passwordRegex, {
    message: '密码必须包含大小写字母、数字、特殊符号中的至少两种',
  });
