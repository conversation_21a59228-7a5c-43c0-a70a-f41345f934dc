// 定义工具类型，将对象的所有属性类型从 number 转换为 string
type ConvertNumberToString<T> = {
  [K in keyof T]: T[K] extends number ? string : T[K];
};

// 类型转换函数
export function convertObject<T extends object>(
  obj: T
): ConvertNumberToString<T> {
  const result: Partial<ConvertNumberToString<T>> = {};

  for (const key in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      const value = obj[key];
      if (typeof value === 'number') {
        result[key as keyof T] =
          value.toString() as ConvertNumberToString<T>[keyof T];
      } else {
        result[key as keyof T] = value as ConvertNumberToString<T>[keyof T];
      }
    }
  }

  return result as ConvertNumberToString<T>;
}

// 定义工具类型，将对象的所有属性类型从 string 转换为 number
export type ConvertStringToNumber<T> = {
  [K in keyof T]: T[K] extends string ? number : T[K];
};

export function convertObjectToNumber<T extends object>(
  obj: T
): ConvertStringToNumber<T> {
  const result: Partial<ConvertStringToNumber<T>> = {};

  for (const key in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      const value = obj[key];
      if (typeof value === 'string' && !isNaN(Number(value))) {
        result[key as keyof T] = Number(
          value
        ) as ConvertStringToNumber<T>[keyof T];
      } else {
        result[key as keyof T] = value as ConvertStringToNumber<T>[keyof T];
      }
    }
  }

  return result as ConvertStringToNumber<T>;
}
