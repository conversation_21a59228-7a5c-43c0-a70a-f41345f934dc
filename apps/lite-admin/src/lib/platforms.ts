import DouYinIcon from '@/assets/platforms/dou-yin.png';
import KuaiShouIcon from '@/assets/platforms/kuai-shou.png';
import WeiXinShiPinHaoIcon from '@/assets/platforms/wei-xin-shi-pin-hao.png';
import XiaoHongShuIcon from '@/assets/platforms/xiao-hong-shu.png';
import BilibiliIcon from '@/assets/platforms/bilibili.png';
import BaiJiaHaoIcon from '@/assets/platforms/bai-jia-hao.png';
import TouTiaoHaoIcon from '@/assets/platforms/tou-tiao-hao.png';
import XinLangWeiBoIcon from '@/assets/platforms/xin-lang-wei-bo.png';
import ZhiHuIcon from '@/assets/platforms/zhihu.png';
import DaYuHaoIcon from '@/assets/platforms/da-yu-hao.png';
import QiEHaoIcon from '@/assets/platforms/qi-e-hao.png';
import SouHuHaoIcon from '@/assets/platforms/sou-hu-hao.png';
import YiDianHaoIcon from '@/assets/platforms/yi-dian-hao.png';
import WangYiHaoIcon from '@/assets/platforms/wang-yi-hao.png';
import AiQiYiIcon from '@/assets/platforms/ai-qi-yi.png';
import TengXunWeiShiIcon from '@/assets/platforms/teng-xun-wei-shi.png';
import UnknownPlatformIcon from '@/assets/platforms/unknow.png';
import WeiXinGongZhongHaoIcon from '@/assets/platforms/wei-xin-gong-zhong-hao.png';
import WeiXinGongZhongHaoBigIcon from '@/assets/platforms/wei-xin-gong-zhong-hao-BIG.png';
import WeiXinIcon from '@/assets/platforms//wei-xin.png';
import WeiXinShiPinHao3rdPartyIcon from '@/assets/platforms/wei-xin-shi-pin-hao-3rd-party.png';

export const platformNames = {
  DouYin: '抖音' as const,
  KuaiShou: '快手' as const,
  WeiXinShiPinHao: '视频号' as const,
  BiliBili: '哔哩哔哩' as const,
  XiaoHongShu: '小红书' as const,
  BaiJiaHao: '百家号' as const,
  TouTiaoHao: '头条号' as const,
  XiGuaShiPin: '西瓜视频' as const,
  ZhiHu: '知乎' as const,
  QiEHao: '企鹅号' as const,
  XinLangWeiBo: '新浪微博' as const,
  SouHuHao: '搜狐号' as const,
  YiDianHao: '一点号' as const,
  DaYuHao: '大鱼号' as const,
  WangYiHao: '网易号' as const,
  AiQiYi: '爱奇艺' as const,
  TengXunWeiShi: '腾讯微视' as const,
  WeiXinGongZhongHao: '微信公众号' as const,
  WeiXin: '微信' as const,
};

export class Platform {
  constructor(
    public name: string,
    public icon: string,
    public bigIcon: string
  ) {}

  static of(
    name: string,
    icon: string,
    bigIcon: string,
    displayOrder: number
  ): Platform {
    const platform = new Platform(name, icon, bigIcon);
    platform.displayOrder = displayOrder;
    return platform;
  }
  displayOrder: number = Number.MAX_SAFE_INTEGER;
}

export const platforms = {
  DouYin: Platform.of(platformNames.DouYin, DouYinIcon, DouYinIcon, 1),
  KuaiShou: Platform.of(platformNames.KuaiShou, KuaiShouIcon, KuaiShouIcon, 2),
  WeiXinShiPinHao: Platform.of(
    platformNames.WeiXinShiPinHao,
    WeiXinShiPinHaoIcon,
    WeiXinShiPinHaoIcon,
    3
  ),
  XiaoHongShu: Platform.of(
    platformNames.XiaoHongShu,
    XiaoHongShuIcon,
    XiaoHongShuIcon,
    4
  ),
  Bilibili: Platform.of(platformNames.BiliBili, BilibiliIcon, BilibiliIcon, 5),
  BaiJiaHao: Platform.of(
    platformNames.BaiJiaHao,
    BaiJiaHaoIcon,
    BaiJiaHaoIcon,
    6
  ),
  TouTiaoHao: Platform.of(
    platformNames.TouTiaoHao,
    TouTiaoHaoIcon,
    TouTiaoHaoIcon,
    7
  ),
  XinLangWeiBo: Platform.of(
    platformNames.XinLangWeiBo,
    XinLangWeiBoIcon,
    XinLangWeiBoIcon,
    8
  ),
  ZhiHu: Platform.of(platformNames.ZhiHu, ZhiHuIcon, ZhiHuIcon, 9),
  QiEHao: Platform.of(platformNames.QiEHao, QiEHaoIcon, QiEHaoIcon, 10),
  SouHuHao: Platform.of(platformNames.SouHuHao, SouHuHaoIcon, SouHuHaoIcon, 11),
  YiDianHao: Platform.of(
    platformNames.YiDianHao,
    YiDianHaoIcon,
    YiDianHaoIcon,
    12
  ),
  DaYuHao: Platform.of(platformNames.DaYuHao, DaYuHaoIcon, DaYuHaoIcon, 13),
  WangYiHao: Platform.of(
    platformNames.WangYiHao,
    WangYiHaoIcon,
    WangYiHaoIcon,
    14
  ),
  AiQiYi: Platform.of(platformNames.AiQiYi, AiQiYiIcon, AiQiYiIcon, 15),
  TengXunWeiShi: Platform.of(
    platformNames.TengXunWeiShi,
    TengXunWeiShiIcon,
    TengXunWeiShiIcon,
    16
  ),
  WeiXin: Platform.of(platformNames.WeiXin, WeiXinIcon, WeiXinIcon, 9),
  WeiXinGongZhongHao: Platform.of(
    platformNames.WeiXinGongZhongHao,
    WeiXinGongZhongHaoIcon,
    WeiXinGongZhongHaoBigIcon,
    9
  ),
};

const UnKnownPlatform = Platform.of(
  '未知',
  UnknownPlatformIcon,
  UnknownPlatformIcon,
  1000
);

const WeiXinGongZhongHaoSub = Platform.of(
  '微信子公众号',
  WeiXinShiPinHao3rdPartyIcon,
  WeiXinShiPinHao3rdPartyIcon,
  1000
);

export const allPlatform = Object.values(platforms);

export function getPlatformByName(name: string, isSubWeixin = false): Platform {
  if (isSubWeixin) return WeiXinGongZhongHaoSub;
  const platform = allPlatform.find((platform) => platform.name === name);
  if (!platform) {
    return UnKnownPlatform;
  }
  return platform;
}
