export type PageCountQuery = {
  skipCount?: number;
  maxResultCount?: number;
  version?: string;
  type?: string;
} & PageQuery;

export interface PageResult<T> {
  items?: T[];
  totalCount?: number;
  data: Array<T>;
  page: number;
  size: number;
  total?: number;
  totalSize?: number;
  totalPage?: number;
}

export type PageQuery = {
  page?: number;
  size?: number;
};

export interface Response<T> {
  data: T; // 实际数据
  message: string; // 状态消息
  statusCode: number; // 状态码
}

export type PageData<T> = {
  data: Array<T>;
  page: number;
  totalSize: number;
  totalPage: number;
};
