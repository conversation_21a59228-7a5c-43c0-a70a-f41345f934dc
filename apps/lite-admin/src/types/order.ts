import { PageCountQuery } from '@/types/index.ts';

export type OrderStatus = 'pending' | 'paid' | 'cancelled' | 'refunded';
export type OrderType = 'create' | 'renew' | 'upgrade' | 'gift';
export type PayType = 'wechatPay' | 'alipay' | 'corporateTransfer' | 'other';

export class OrderStatusMap {
  static pending = '待支付';
  static paid = '已支付';
  static cancelled = '已取消';
  static refunded = '已退费';

  static getStatus(status: OrderStatus) {
    return OrderStatusMap[status];
  }
}

export class OrderTypeMap {
  static create = '开通';
  static Create = '开通';
  static renew = '续费';
  static upgrade = '升级';
  static gift = '赠送';

  static getType(type: OrderType) {
    return OrderTypeMap[type] || '-';
  }
}

export class PayTypeMap {
  static wechatPay = '微信';
  static alipay = '支付宝';
  static corporateTransfer = '对公转账';
  static other = '其他';

  static getType(type: PayType) {
    return PayTypeMap[type];
  }
}

export const OrderSourceMap: Record<string, string> = {
  online: '线上',
  system: '系统',
};

export type Order = {
  id: string;
  orderNo: string;
  teamName: string;
  code: string;
  phone: string;
  orderStatus: OrderStatus;
  orderType: OrderType;
  payType: PayType;
  totalAmount: string;
  createdAt: number;
  payTime: number;
  payAmount: string;
  customerName: string;
  salesType: number;
};

export type OrderDetail = {
  id: string;
  orderNo: string;
  teamName: string;
  code: string;
  orderStatus: OrderStatus;
  orderType: OrderType;
  payType: PayType;
  totalAmount: string;
  createdAt: number;
  payTime: number;
  expiredAt: number;
  interestCount: number;
  vipMonth: number;
  freeMonth: number;
  days: number;
  isFree: boolean;
  creatorName: string;
  remark: string;
  orderSource: string;
  payAmount: string;
  payableAmount: string;
};

export interface OrderListParams extends PageCountQuery {
  orderNo?: string;
  teamName?: string;
  phone?: string;
  orderStatus?: string;
  orderType?: string;
  payType?: string;
  createStartTime?: number | null;
  createEndTime?: number | null;
  payStartTime?: number | null;
  payEndTime?: number | null;
}

export type OrderCreateParams = {
  /**
   * 权益包数量
   */
  interestCount: number;
  /**
   * 权益包id
   */
  interestId: string;
  isPay: boolean;
  /**
   * 月份数量
   */
  month?: number;
  /**
   * 实付金额
   */
  payAmount: number;
  remark?: string;
  /**
   * 团队id
   */
  teamId: string;
  days: number;
};

// vip规格
export type Interest = {
  id: string;
  platformAccountCount: number;
  capacityLimit: number;
  appPublish: boolean;
  memberCount: number;
  price: number;
  vipOften: {
    mount: number;
    present: number;
  }[];
};

export type PriceSum = {
  orderAmount: number;
  discountAmount: number;
  expiredTime: number;
  tips: string;
  tipsCn: string;
};

export type PriceSumParams = {
  // 订单类型
  orderType?: OrderType;
  // 团队id
  teamId?: string;
  // 权益包id
  interestId?: string;
  // 权益包数量
  interestCount?: number;
  // 开通时长
  month?: number;
  days?: number;
};

export type renewParams = {
  teamId: string;
  interestId: string;
  month: number;
  payAmount: number;
  isPay: boolean;
  remark?: string;
  days: number;
};

// 升级
export type upgradeParams = {
  isPay: boolean;
  teamId: string;
  interestId: string;
  interestCount: number;
  payAmount: number;
  remark?: string;
};

// 赠送
export type giftParams = {
  teamId: string;
  interestId: string;
  giftDays: number;
  remark?: string;
};

// 退款 入参
export type RefundParams = {
  realityPrice: number;
  remark: string;
};

// 获取退款列表
export type RefundOutput = {
  // 退款编号
  refundNo: string;
  // 退款金额
  refundableAmount: string;
  // 实退金额
  actualAmount: string;
  // 备注
  remark: string;
  // 退费时间
  createdAt: string;
  //订单列表
  // orderInfos: Array<any>;
};

export type RefundRecord = {
  orderNo: string;
  refundAmount: string;
  actualRefundAmount: string;
};

export type RefundList = {
  refundNo: string;
  refundableAmount: number;
  actualAmount: number;
  remark: string;
  creatorName: string;
  createdAt: string;
  orderInfos: [
    {
      orderNo: string;
      refundableAmount: number;
      actualAmount: number;
      refundAmount: string;
      actualRefundAmount: string;
    },
  ];
};
