export interface Account {
  id: string;
  name: string;
  username: string;
  // 角色：0超管，1管理员，2客服
  role: 0 | 1 | 2;
  qrCode: string;
  // 状态0不可分配 1可以分配
  status: 0 | 1;
  createdAt: number;
}

export interface AccountReq {
  name: string;
  username: string;
  password?: string;
  role: number;
  status: number;
  qrCode?: string;
}

export interface ServiceAccount {
  /**
   * 创建时间
   */
  createdAt: number;
  /**
   * 用户ID
   */
  id: string;
  /**
   * 姓名
   */
  name: string;
  /**
   * 客服二维码地址
   */
  qrCode: string;
  /**
   * 角色：0超管，1管理员，2客服
   */
  role: number;
  /**
   * 状态0不可分配 1可以分配
   */
  status: number;
  /**
   * 账号
   */
  username: string;
}
