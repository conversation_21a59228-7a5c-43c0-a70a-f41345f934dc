export enum OS {
  Windows = 'windows',
  MacOS = 'macos',
  IOS = 'ios',
  Android = 'android',
}
export type Desktop = {
  id: string;
  type: OS;
  publishName: string;
  createdAt: string;
  version: string;
  requestUrl: string;
  isForce: boolean;
  notice: string;
};

export type DesktopDetail = {
  id: string;
  type: string;
  version: string;
  requestUrl: string;
  isForce: boolean;
  notice: string;
};

export class DesktopVersionListInput {
  publishStartTime?: Date;
  publishEndTime?: Date;
}

export type DesktopUploadUrl = {
  serviceUrl: string;
  key: string;
};

export type AddDesktopConfigInput = {
  id?: string;
  type: string;
  version: string;
  isForce: boolean;
  notice: string;
  file?: File;
  requestUrl?: string;
};
