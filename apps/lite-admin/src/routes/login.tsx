import { createFile<PERSON>oute, useRouter } from '@tanstack/react-router';
import { z } from 'zod';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@mono/ui/card';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@mono/ui/form';
import { Input } from '@mono/ui/input';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { LoadingButton } from '@/components/loading-button';
import { useMutation } from '@tanstack/react-query';
import { authorize } from '@/composables/use-authorize';
import { userAuth, userTwoFactorAuth } from '@/api/user';
import { Authorization } from '@/types/authorization';
import { UserLoginOutput } from '@/types/user';
import { useUserStore } from '@/store/user.ts';
import { LoginAuthenticator } from '@/components/login/loginAuthenticator';
import { useCallback, useRef, useState } from 'react';

export const Route = createFileRoute('/login')({
  validateSearch: z.object({ redirect: z.string().optional() }),
  component: LoginComponent,
});

const formSchema = z.object({
  username: z.string().min(1, '请输入账号'),
  password: z.string().min(1, '请输入密码'),
});

// 开发环境默认填充管理员账号
const mDefaultValues =
  import.meta.env.DEV ?
    {
      username: 'admin',
      password: 'admin1234',
    }
  : {
      username: '',
      password: '',
    };

/**
 * 登录视图
 */
function LoginComponent() {
  const router = useRouter();
  const search = Route.useSearch();

  const setIsSuper = useUserStore((state) => state.setIsSuper);
  const userLoginDataRef = useRef<UserLoginOutput>();
  const [open, setOpen] = useState(false);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: mDefaultValues,
  });

  // oidc-client-ts 认证方式登录
  // const mutation = useMutation({
  //   // mutationFn: (values: z.infer<typeof formSchema>) => userManager.signinResourceOwnerCredentials(values),
  //   mutationFn: userManager.signinResourceOwnerCredentials.bind(userManager),
  //   mutationKey: ["signinResourceOwnerCredentials"],
  //   onSuccess: (data: User) => {
  //     // console.log("%c onSuccess->data:\n", "color:#FF7A45", data);
  //     authorize({...data});
  //     const mainRoute = search.redirect ?? "/";
  //     router.history.replace(mainRoute);
  //   },
  //   onError: (error) => {
  //     // 登录失败
  //     console.debug(error);
  //   },
  // });

  const loginNext = useCallback(
    (data: UserLoginOutput) => {
      const auth: Authorization = {
        access_token: data.authorization,
      };
      setIsSuper(data.isSuper);
      authorize(auth, data.name);
      const mainRoute = search.redirect ?? '/';
      router.history.replace(mainRoute);
    },
    [router.history, search.redirect, setIsSuper]
  );

  const mutation = useMutation({
    mutationFn: userAuth,
    mutationKey: ['userAuth'],
    onSuccess: (data) => {
      userLoginDataRef.current = data;
      if (data.initMfa || data.verifyMfa) {
        setOpen(true);
      } else {
        loginNext(data);
      }
    },
    onError: (error) => {
      // 登录失败
      console.debug(error);
    },
  });

  const twoFactorAuthMutation = useMutation({
    mutationFn: userTwoFactorAuth,
    onSuccess: (data) => {
      loginNext(data);
    },
    onError: (error) => {
      // 二次验证mfa-登录失败
      console.log(error);
    },
  });

  const onSubmit = (values: z.infer<typeof formSchema>) => {
    // Do something with the form values.
    // ✅ This will be type-safe and validated.
    console.debug(values);
    mutation.mutate(values);
  };

  return (
    <main className="flex flex-row items-center justify-center w-screen h-screen">
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="w-full max-w-sm"
        >
          <Card className="w-full">
            <CardHeader>
              <CardTitle className="text-2xl">登 录</CardTitle>
              <CardDescription>蚁小二Lite后台管理系统</CardDescription>
            </CardHeader>

            <CardContent className="grid grid-cols-1 gap-4">
              <FormField
                control={form.control}
                name="username"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>账号</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>密码</FormLabel>
                    <FormControl>
                      <Input type="password" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>

            <CardFooter>
              <LoadingButton
                isPending={mutation.isPending}
                disabled={mutation.isPending}
                className="w-full"
                type="submit"
              >
                登录
              </LoadingButton>
            </CardFooter>
          </Card>
        </form>
      </Form>
      <LoginAuthenticator
        open={open}
        setOpen={setOpen}
        onComplete={(token: string) => {
          const userLoginData = userLoginDataRef.current;
          twoFactorAuthMutation.mutate({
            username: form.getValues().username,
            password: form.getValues().password,
            secret: userLoginData?.initMfa ? userLoginData.secret : undefined,
            token: token,
          });
        }}
        data={userLoginDataRef.current!}
      />
    </main>
  );
}
