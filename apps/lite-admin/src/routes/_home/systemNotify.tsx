import { useMemo, useRef, useState } from 'react';
import { Button } from '@mono/ui/button';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { createFileRoute } from '@tanstack/react-router';
import { DataTable } from '@/components/dataTable';
import { PaginationState } from '@tanstack/react-table';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@mono/ui/alert-dialog';
import { Input } from '@mono/ui/input';
import { SystemNotifySetting } from '@/components/systemNotify/setting';
import { SystemNotifyDto } from '@/types/systemNotify.ts';
import { getTableColumns } from '@/components/systemNotify/columns';
import { systemNotifyList, deleteSystemNotify } from '@/api/systemNotify';

export const Route = createFileRoute('/_home/systemNotify')({
  component: SystemNotify,
});

function SystemNotify() {
  const queryClient = useQueryClient();
  const [open, setOpen] = useState(false);
  const [dialog, setDialog] = useState(false);
  const mSystemNotify = useRef({} as SystemNotifyDto);
  const [name, setName] = useState('');
  const nameRef = useRef(name);

  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  });

  // 搜索逻辑
  const handleSearch = () => {
    setPagination((prev) => ({ ...prev, pageIndex: 0 })); // 重置分页
    query.refetch();
  };

  const columns = useMemo(() => {
    return getTableColumns((systemNotify: SystemNotifyDto, method: string) => {
      mSystemNotify.current = systemNotify;
      if (method == 'edit') {
        setOpen(true);
      }
      if (method == 'delete') {
        setDialog(true);
      }
    });
  }, []);

  const query = useQuery({
    queryKey: ['systemNotifyList', nameRef.current, pagination],
    queryFn: () =>
      systemNotifyList({
        page: pagination.pageIndex + 1,
        size: pagination.pageSize,
        name: name,
      }),
  });

  const handelDelete = async (id: string) => {
    await deleteSystemNotify(id);
    await queryClient.refetchQueries({
      queryKey: ['systemNotifyList'],
    });
  };

  const handleClick = () => {
    mSystemNotify.current = {
      id: '',
      title: '',
      content: '',
      type: 'system',
      isPopUp: false,
      createdAt: 0,
    };
    setOpen(true); // 打开弹框
  };

  return (
    <div className="flex flex-col w-full h-full overflow-hidden">
      <div className="flex pt-1 pb-4 px-1 gap-4">
        <Input
          className="w-[220px]"
          placeholder="名称搜索"
          value={name}
          onChange={(e) => setName(e.target.value)}
        />
        <Button type="button" onClick={handleSearch}>
          搜索
        </Button>

        <Button onClick={() => handleClick()}>添加系统公告</Button>
      </div>

      <DataTable
        columns={columns}
        data={query.data?.data}
        rowCount={query.data?.totalSize}
        pagination={pagination}
        setPagination={setPagination}
      />

      <SystemNotifySetting
        systemNotify={mSystemNotify.current}
        open={open}
        setOpen={setOpen}
      />

      <AlertDialog open={dialog} onOpenChange={setDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确定删除吗?</AlertDialogTitle>
            <AlertDialogDescription></AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => handelDelete(mSystemNotify.current.id)}
            >
              确定
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
