import { createFileRoute } from '@tanstack/react-router';
import { Ta<PERSON>, Ta<PERSON>List, TabsTrigger } from '@mono/ui/tabs';
import { useEffect, useMemo, useState } from 'react';
import { DataTable } from '@/components/dataTable';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { ColumnDef, PaginationState } from '@tanstack/react-table';
import { Button } from '@mono/ui/button';
import { ScriptUploadModal } from '@/components/script-modal/ScriptUploadModal.tsx';
import { getScriptList } from '@/api/script.ts';
import { format } from 'date-fns';
import { IScriptType } from '@/types/scriptType';

export const Route = createFileRoute('/_home/updateScript')({
  component: UpdateScript,
});

function UpdateList({
  type,
  open,
  setOpen,
}: {
  type: 'spider' | 'rpa';
  open: boolean;
  setOpen: (open: boolean) => void;
}) {
  const queryClient = useQueryClient();
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  });

  const query = useQuery({
    queryKey: ['getScriptList', pagination, type],
    queryFn: () =>
      getScriptList(
        {
          page: pagination.pageIndex + 1,
          size: pagination.pageSize,
        },
        type
      ),
  });

  useEffect(() => {
    setPagination({
      pageIndex: 0,
      pageSize: 10,
    });
  }, [type]);

  const columns = useMemo<Array<ColumnDef<IScriptType>>>(() => {
    return [
      {
        header: '版本',
        accessor: 'name',
        cell: ({ row }) => {
          return <span>{row.original.version}</span>;
        },
      },
      {
        header: '发布时间',
        accessor: 'username',
        cell: ({ row }) => {
          return (
            <span>{format(row.original.createdAt, 'yyyy-MM-dd HH:mm:ss')}</span>
          );
        },
      },
    ];
  }, []);

  const onSuccess = async () => {
    await queryClient.refetchQueries({
      queryKey: ['getScriptList'],
    });
  };

  return (
    <div className="flex flex-col gap-2 flex-1 overflow-hidden">
      <DataTable
        columns={columns}
        data={query.data?.data}
        rowCount={query.data?.totalSize}
        pagination={pagination}
        setPagination={setPagination}
      />
      <ScriptUploadModal
        open={open}
        setOpen={setOpen}
        onSuccess={() => onSuccess()}
        ScriptType={type}
      />
    </div>
  );
}

function UpdateScript() {
  const [tab, setTab] = useState<'spider' | 'rpa'>('spider');
  const [open, setOpen] = useState<boolean>(false);
  return (
    <div className="w-full h-full flex flex-col gap-2 overflow-hidden">
      <div className="flex gap-6">
        <Tabs
          value={tab}
          onValueChange={(value) => setTab(value as 'spider' | 'rpa')}
        >
          <TabsList>
            <TabsTrigger value="spider">爬虫脚本</TabsTrigger>
            <TabsTrigger value="rpa">浏览器脚本</TabsTrigger>
          </TabsList>
        </Tabs>
        <Button onClick={() => setOpen(true)}>新增版本</Button>
      </div>
      <UpdateList type={tab} open={open} setOpen={setOpen} />
    </div>
  );
}
