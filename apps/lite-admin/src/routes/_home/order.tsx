import { OrderPage } from '@/components/order';
import { createFileRoute } from '@tanstack/react-router';
import { z } from 'zod';

const searchSchema = z.object({
  channelCode: z.string().optional(),
  salesType: z.number().optional(),
  createStartTime: z.number().optional(),
  createEndTime: z.number().optional(),
  customerId: z.string().optional(),
  orderStatus: z.string().optional(),
});

export const Route = createFileRoute('/_home/order')({
  validateSearch: searchSchema,
  component: OrderPage,
});
