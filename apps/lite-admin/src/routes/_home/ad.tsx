import { useMemo, useRef, useState } from 'react';
import { Button } from '@mono/ui/button';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { createFileRoute } from '@tanstack/react-router';
import { DataTable } from '@/components/dataTable';
import { PaginationState } from '@tanstack/react-table';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@mono/ui/alert-dialog';
import { Input } from '@mono/ui/input';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@mono/ui/select';
import { AdSetting } from '@/components/ad/setting';
import { AdDto } from '@/types/ad';
import { getTableColumns } from '@/components/ad/columns';
import { adList, deleteAd, putAdEnabled } from '@/api/ad';

export const Route = createFileRoute('/_home/ad')({
  component: Ad,
});

function Ad() {
  const queryClient = useQueryClient();
  const [open, setOpen] = useState(false);
  const [dialog, setDialog] = useState(false);
  const mAd = useRef({} as AdDto);
  const [name, setName] = useState('');
  const [enabled, setEnabled] = useState('');
  const [adType, setAdType] = useState('');
  const nameRef = useRef(name);
  const enabledRef = useRef(enabled);

  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  });

  // 搜索逻辑
  const handleSearch = () => {
    setPagination((prev) => ({ ...prev, pageIndex: 0 })); // 重置分页
    query.refetch();
  };

  const columns = useMemo(() => {
    return getTableColumns(
      (ad: AdDto, method: string) => {
        mAd.current = ad;
        if (method == 'edit') {
          setOpen(true);
        }
        if (method == 'delete') {
          setDialog(true);
        }
      },
      async (id: string, enabled: boolean) => {
        await putAdEnabled(id, enabled);
        query.refetch();
      }
    );
  }, []);

  const query = useQuery({
    queryKey: ['adList', nameRef.current, enabledRef.current, pagination],
    queryFn: () =>
      adList({
        page: pagination.pageIndex + 1,
        size: pagination.pageSize,
        name: name,
        enabled: enabled,
        adType: adType,
      }),
  });

  const handelDelete = async (id: string) => {
    await deleteAd(id);
    await queryClient.refetchQueries({
      queryKey: ['adList'],
    });
  };

  const handleClick = () => {
    mAd.current = {
      id: '',
      name: '',
      enabled: true,
      sort: 1,
      adUrl: '',
      isTimed: false,
      expiredStartAt: 0,
      expiredEndAt: 0,
      isJumpTo: false,
      jumpToUrl: '',
      adType: '',
      popupType: '',
    };
    setOpen(true); // 打开弹框
  };

  return (
    <div className="flex flex-col w-full h-full overflow-hidden">
      <div className="flex pt-1 pb-4 px-1 gap-4">
        <Input
          className="w-[220px]"
          placeholder="名称搜索"
          value={name}
          onChange={(e) => setName(e.target.value)}
        />
        <div className="flex items-center gap-2  whitespace-nowrap">
          状态筛选:
          <Select defaultValue="all" onValueChange={(e) => setEnabled(e)}>
            <SelectTrigger className="w-[100px]">
              <SelectValue placeholder="请选择状态" />
            </SelectTrigger>
            <SelectContent>
              <SelectGroup>
                <SelectItem value="all">全部</SelectItem>
                <SelectItem value="true">上架</SelectItem>
                <SelectItem value="false">已下架</SelectItem>
              </SelectGroup>
            </SelectContent>
          </Select>
        </div>

        <div className="flex items-center gap-2  whitespace-nowrap">
          类型筛选:
          <Select defaultValue="all" onValueChange={(e) => setAdType(e)}>
            <SelectTrigger className="w-[100px]">
              <SelectValue placeholder="请选择类型" />
            </SelectTrigger>
            <SelectContent>
              <SelectGroup>
                <SelectItem value="all">全部</SelectItem>
                <SelectItem value="banner">Banner</SelectItem>
                <SelectItem value="popup">弹窗</SelectItem>
              </SelectGroup>
            </SelectContent>
          </Select>
        </div>
        <Button type="button" onClick={handleSearch}>
          搜索
        </Button>

        <Button onClick={() => handleClick()}>添加广告位</Button>
      </div>

      <DataTable
        columns={columns}
        data={query.data?.data}
        rowCount={query.data?.totalSize}
        pagination={pagination}
        setPagination={setPagination}
      />

      <AdSetting ad={mAd.current} open={open} setOpen={setOpen} />

      <AlertDialog open={dialog} onOpenChange={setDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确定删除吗?</AlertDialogTitle>
            <AlertDialogDescription></AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction onClick={() => handelDelete(mAd.current.id)}>
              确定
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
