import { createFileRoute } from '@tanstack/react-router';
import { Card, CardContent, CardHeader, CardTitle } from '@mono/ui/card';
import { useQuery } from '@tanstack/react-query';
import { getDataScale, getDataStatistic } from '@/api/data';
import { ChartConfig, ChartContainer } from '@mono/ui/chart';
import { ScrollArea } from '@mono/ui/scroll-area';
import { RegisterScale } from '@/components/overviews/register';
import { AccountScale } from '@/components/overviews/account';
import { AreaChartComponent } from '@/components/overviews/AreaChart';
import { Publish } from '@/components/overviews/publish';
import { PieCharts } from '@/components/overviews/PieCharts.tsx';
import React from 'react';

export const Route = createFileRoute('/_home/data')({
  component: DataRoot,
});

const chartConfig = {
  visitors: {
    label: '数量',
  },
  articlePublishNum: {
    label: '文章',
    color: 'hsl(var(--chart-1))',
  },
  textPublishNum: {
    label: '图文',
    color: 'hsl(var(--chart-2))',
  },
  videoPublishNum: {
    label: '视频',
    color: 'hsl(var(--chart-3))',
  },
  oneClickPublishNum: {
    label: '桌面端一键发布',
    color: 'hsl(var(--chart-1))',
  },
  browserPublishNum: {
    label: '浏览器发布',
    color: 'hsl(var(--chart-2))',
  },
  appPublishNum: {
    label: 'APP发布',
    color: 'hsl(var(--chart-3))',
  },
  platformAccountNum: {
    label: '账号数',
    color: 'hsl(var(--chart-1))',
  },
  browserNum: {
    label: '网站数',
    color: 'hsl(var(--chart-2))',
  },
} satisfies ChartConfig;

function DataRoot() {
  const dataStatisticQuery = useQuery({
    queryKey: ['dataStatistic'],
    queryFn: getDataStatistic,
  });

  const dataScaleQuery = useQuery({
    queryKey: ['dataScale'],
    queryFn: getDataScale,
  });

  const chartData = Array<{
    browser: string;
    visitors: number;
    fill: string;
  }>();
  const channelChartData = Array<{
    browser: string;
    visitors: number;
    fill: string;
  }>();
  const accountChartData = Array<{
    browser: string;
    visitors: number;
    fill: string;
  }>();
  if (
    dataScaleQuery.data?.articlePublishNum &&
    dataScaleQuery.data?.articlePublishNum > 0
  ) {
    chartData.push({
      browser: 'articlePublishNum',
      visitors: dataScaleQuery.data?.articlePublishNum,
      fill: 'var(--color-articlePublishNum)',
    });
  }
  if (
    dataScaleQuery.data?.textPublishNum &&
    dataScaleQuery.data?.textPublishNum > 0
  ) {
    chartData.push({
      browser: 'textPublishNum',
      visitors: dataScaleQuery.data?.textPublishNum,
      fill: 'var(--color-textPublishNum)',
    });
  }
  if (
    dataScaleQuery.data?.videoPublishNum &&
    dataScaleQuery.data?.videoPublishNum > 0
  ) {
    chartData.push({
      browser: 'videoPublishNum',
      visitors: dataScaleQuery.data?.videoPublishNum,
      fill: 'var(--color-videoPublishNum)',
    });
  }

  if (
    dataScaleQuery.data?.oneClickPublishNum &&
    dataScaleQuery.data?.oneClickPublishNum > 0
  ) {
    channelChartData.push({
      browser: 'oneClickPublishNum',
      visitors: dataScaleQuery.data?.oneClickPublishNum,
      fill: 'var(--color-oneClickPublishNum)',
    });
  }
  if (
    dataScaleQuery.data?.browserPublishNum &&
    dataScaleQuery.data?.browserPublishNum > 0
  ) {
    channelChartData.push({
      browser: 'browserPublishNum',
      visitors: dataScaleQuery.data?.browserPublishNum,
      fill: 'var(--color-browserPublishNum)',
    });
  }
  if (
    dataScaleQuery.data?.appPublishNum &&
    dataScaleQuery.data?.appPublishNum > 0
  ) {
    channelChartData.push({
      browser: 'appPublishNum',
      visitors: dataScaleQuery.data?.appPublishNum,
      fill: 'var(--color-appPublishNum)',
    });
  }
  if (
    dataScaleQuery.data?.platformAccountNum &&
    dataScaleQuery.data?.platformAccountNum > 0
  ) {
    accountChartData.push({
      browser: 'platformAccountNum',
      visitors: dataScaleQuery.data?.platformAccountNum,
      fill: 'var(--color-platformAccountNum)',
    });
  }
  if (dataScaleQuery.data?.browserNum && dataScaleQuery.data?.browserNum > 0) {
    accountChartData.push({
      browser: 'browserNum',
      visitors: dataScaleQuery.data?.browserNum,
      fill: 'var(--color-browserNum)',
    });
  }

  // 发布类型占比
  const formatChartData = React.useMemo(() => {
    return chartData.map((item) => ({
      name:
        chartConfig[item.browser as keyof typeof chartConfig]?.label ??
        item.browser,
      value: item.visitors,
    }));
  }, [chartData]);

  // 发布渠道占比
  const formatChannelChartData = React.useMemo(() => {
    return channelChartData.map((item) => ({
      name:
        chartConfig[item.browser as keyof typeof chartConfig]?.label ??
        item.browser,
      value: item.visitors,
    }));
  }, [channelChartData]);

  // 账号&网站占比
  const formatAccountChartData = React.useMemo(() => {
    return accountChartData.map((item) => ({
      name:
        chartConfig[item.browser as keyof typeof chartConfig]?.label ??
        item.browser,
      value: item.visitors,
    }));
  }, [accountChartData]);

  return (
    <ScrollArea className="flex flex-col overflow-hidden">
      <div className="flex flex-col gap-4">
        <div className="flex items-center gap-4">
          <AreaChartComponent
            title="30日作品发布量"
            chartData={
              dataStatisticQuery.data?.data.map((item) => ({
                date: item.date,
                value: item.publishCount,
              })) ?? []
            }
          />
          <AreaChartComponent
            title="30日活跃发布团队数"
            chartData={
              dataStatisticQuery.data?.data.map((item) => ({
                date: item.date,
                value: item.teamPublishCount,
              })) ?? []
            }
          />
        </div>
        <div className="flex items-center gap-4">
          <AreaChartComponent
            title="30日APP发布用户数"
            chartData={
              dataStatisticQuery.data?.data.map((item) => ({
                date: item.date,
                value: item.appUserPublishCount,
              })) ?? []
            }
          />
          <AreaChartComponent
            title="30日新增用户数"
            chartData={
              dataStatisticQuery.data?.data.map((item) => ({
                date: item.date,
                value: item.registerCount,
              })) ?? []
            }
          />
        </div>
        <div className="flex flex-wrap justify-center items-center gap-4">
          <div className="flex-1 min-w-[300px] ">
            <Card className="w-full">
              <CardHeader>
                <CardTitle className="text-xl">发布类型占比</CardTitle>
              </CardHeader>
              <CardContent>
                <ChartContainer config={chartConfig} className="w-full">
                  <PieCharts
                    chartData={formatChartData}
                    chartsClass={'!w-full !h-full'}
                  />
                </ChartContainer>
              </CardContent>
            </Card>
          </div>
          <div className="flex-1 min-w-[300px] ">
            <Card className="w-full">
              <CardHeader>
                <CardTitle className="text-xl">发布渠道占比</CardTitle>
              </CardHeader>
              <CardContent>
                <ChartContainer config={chartConfig} className="w-full">
                  <PieCharts
                    chartData={formatChannelChartData}
                    chartsClass={'!w-full !h-full'}
                  />
                </ChartContainer>
              </CardContent>
            </Card>
          </div>
          <div className="flex-1 min-w-[300px]">
            <RegisterScale />
          </div>
          <div className="flex-1 min-w-[300px] ">
            <Card className="w-full">
              <CardHeader>
                <CardTitle className="text-xl">账号&网站</CardTitle>
              </CardHeader>
              <CardContent>
                <ChartContainer config={chartConfig} className="w-full">
                  <PieCharts
                    chartData={formatAccountChartData}
                    chartsClass={'!w-full !h-full'}
                  />
                </ChartContainer>
              </CardContent>
            </Card>
          </div>
        </div>
        <div className="h-[500px] overflow-hidden">
          <Publish />
        </div>
        <div className="h-[500px] overflow-hidden">
          <AccountScale />
        </div>
      </div>
    </ScrollArea>
  );
}
