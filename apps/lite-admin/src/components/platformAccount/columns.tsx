import { ColumnDef } from '@tanstack/react-table';
import { formatDate } from '@mono/utils/day';
import { AvatarCard } from '@mono/ui/common/AvatarCard';
import { Badge } from '@mono/ui/badge';
import { ceil, keyBy } from 'lodash';
import { PlatformAccount } from '@/types/platformAccount';
import { getPlatformByName } from '@/lib/platforms';
import { cn } from '@/lib/utils';
// import { Button } from "@mono/ui/button";

export const loginStatus = [
  // {
  //   value: 0,
  //   label: '未登录',
  // },
  {
    value: 1,
    label: '登录成功',
  },
  {
    value: 2,
    label: '登录过期',
  },
  // {
  //   value: 3,
  //   label: '登录失败',
  // },
  {
    value: 4,
    label: '取消授权',
  },
];

const statusMap = keyBy(loginStatus, 'value');

export function getTableColumns(): Array<ColumnDef<PlatformAccount>> {
  return [
    {
      header: '账号',
      accessorKey: 'platformAccountName',
      cell: ({ row }) => {
        const { platformAccountName, platformName, parentId, remark } =
          row.original;
        return (
          <div className="w-44">
            <AvatarCard
              title={platformAccountName}
              subtitle={remark}
              src={getPlatformByName(platformName, !!parentId)?.icon}
            />
          </div>
        );
      },
    },
    {
      header: '所属团队',
      accessorKey: 'teamCode',
      cell: ({ row }) => {
        const { teamName, teamCode } = row.original;
        return (
          <div className="flex flex-col">
            <span>{teamName}</span>
            <span className="font-medium">{teamCode}</span>
          </div>
        );
      },
    },
    {
      header: '平台',
      accessorKey: 'platformName',
      cell: ({ row }) => {
        const { platformName } = row.original;
        return <Badge variant="outline">{platformName}</Badge>;
      },
    },
    {
      header: '创建时间',
      accessorKey: 'createdAt',
      cell: ({ row }) => {
        const { createdAt } = row.original;
        return (
          <span className="text-muted-foreground">{formatDate(createdAt)}</span>
        );
      },
    },
    {
      header: '更新时间',
      accessorKey: 'updatedAt',
      cell: ({ row }) => {
        const { updatedAt } = row.original;
        return (
          <span className="text-muted-foreground">{formatDate(updatedAt)}</span>
        );
      },
    },
    {
      header: '登录状态',
      accessorKey: 'status',
      cell: ({ row }) => {
        const { status } = row.original;
        return (
          <span
            className={cn(
              { 'text-destructive': status === 2 },
              {
                'text-muted-foreground': status === 4,
              }
            )}
          >
            {statusMap[status].label}
          </span>
        );
      },
    },
    {
      header: '登录状态监测',
      accessorKey: 'cloudCheckTime',
      cell: ({ row }) => {
        const { cloudCheckTime } = row.original;
        return (
          <span className="text-muted-foreground">
            {cloudCheckTime ? formatDate(cloudCheckTime) : '-'}
          </span>
        );
      },
    },
    {
      header: '账号数据',
      accessorKey: 'cloudUpdatedAt',
      cell: ({ row }) => {
        const { cloudUpdatedAt } = row.original;
        return (
          <span className="text-muted-foreground">
            {cloudUpdatedAt ? formatDate(cloudUpdatedAt) : '-'}
          </span>
        );
      },
    },
    {
      header: '登录保持',
      accessorKey: 'onLineDays',
      cell: ({ row }) => {
        const { onLineDays } = row.original;
        return (
          <span className="text-muted-foreground">{ceil(onLineDays)}天</span>
        );
      },
    },
  ];
}
