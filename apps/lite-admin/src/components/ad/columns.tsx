import { ColumnDef } from '@tanstack/react-table';
import { formatDate } from '@mono/utils/day';
import { Button } from '@mono/ui/button';
import { AdDto } from '@/types/ad';
import { Switch } from '@mono/ui/switch';

export function getTableColumns(
  handleSetting: (data: AdDto, method: string) => void,
  settingEnabled: (id: string, enabled: boolean) => void
): Array<ColumnDef<AdDto>> {
  return [
    {
      header: '序列号',
      accessorKey: 'sort',
      cell: ({ row }) => {
        const { sort } = row.original;
        return <span>{sort}</span>;
      },
    },
    {
      header: '名称',
      accessorKey: 'name',
      cell: ({ row }) => {
        const { name } = row.original;
        return <span>{name}</span>;
      },
    },
    {
      header: '缩略图',
      accessorKey: 'adPath',
      size: 423,
      cell: ({ row }) => {
        const { adPath } = row.original;
        return (
          <span>
            <img src={adPath} style={{ width: '423px', height: '118px' }} />
          </span>
        );
      },
    },
    {
      header: '是否跳转',
      accessorKey: 'isJumpTo',
      cell: ({ row }) => {
        const { isJumpTo } = row.original;
        return <span>{isJumpTo === true ? '是' : '否'}</span>;
      },
    },
    {
      header: '跳转地址',
      accessorKey: 'jumpToUrl',
      cell: ({ row }) => {
        const { jumpToUrl } = row.original;
        return <span>{jumpToUrl ? jumpToUrl : '-'}</span>;
      },
    },
    {
      header: '类型',
      accessorKey: 'adType',
      cell: ({ row }) => {
        const { adType } = row.original;
        return <span>{adType === 'banner' ? 'Banner' : '弹窗'}</span>;
      },
    },
    {
      header: '广告有效起止期',
      accessorKey: 'createdAt',
      cell: ({ row }) => {
        const { expiredStartAt, expiredEndAt } = row.original;
        return (
          <span className="text-muted-foreground">
            {expiredStartAt > 0 &&
              expiredEndAt > 0 &&
              new Date(expiredEndAt) > new Date(expiredStartAt) &&
              `${formatDate(expiredStartAt)} - ${formatDate(expiredEndAt)}`}
            {expiredStartAt == 0 && expiredEndAt == 0 && `永久有效`}
          </span>
        );
      },
    },
    {
      header: '上架状态',
      accessorKey: 'enabled',
      cell: ({ row }) => {
        const { enabled, id } = row.original;
        return (
          <span
            className="text-muted-foreground"
            style={{ color: enabled === true ? '#1890ff' : '#F3222D' }}
          >
            <Switch
              checked={enabled}
              onCheckedChange={() => settingEnabled(id, !enabled)}
              style={{
                backgroundColor: enabled ? '#1890ff' : '#F3222D', // 开启和关闭时的背景颜色
                borderColor: enabled ? '#1890ff' : '#F3222D', // 可能需要边框颜色一致
                transition: 'background-color 0.3s ease', // 添加过渡动画
              }}
            />
          </span>
        );
      },
    },
    {
      header: '上次修改人',
      accessorKey: 'adminName',
      cell: ({ row }) => {
        const { adminName } = row.original;
        return <span className="text-muted-foreground">{adminName}</span>;
      },
    },
    {
      header: '操作',
      accessorKey: 'action',
      cell: ({ row }) => {
        const ad = row.original;
        return (
          <>
            <Button variant="ghost" onClick={() => handleSetting(ad, 'edit')}>
              编辑
            </Button>
            <Button variant="ghost" onClick={() => handleSetting(ad, 'delete')}>
              删除
            </Button>
          </>
        );
      },
    },
  ];
}
