import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
  DialogDescription,
} from '@mono/ui/dialog';
import { Input } from '@mono/ui/input';
import { Button } from '@mono/ui/button';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@mono/ui/form';
import { LoadingButton } from '@/components/loading-button';
import { useEffect, useMemo } from 'react';
import { VisuallyHidden } from '@radix-ui/react-visually-hidden';
import { Textarea } from '@mono/ui/textarea';
import { SystemNotifyDto } from '@/types/systemNotify';
import { Switch } from '@mono/ui/switch';
import {
  addSystemNotiryConfig,
  setSystemNotifyConfig,
} from '@/api/systemNotify.ts';

export function SystemNotifySetting({
  systemNotify,
  open,
  setOpen,
}: {
  systemNotify?: SystemNotifyDto;
  open: boolean;
  setOpen: (open: boolean) => void;
}) {
  const queryClient = useQueryClient();

  const formSchema = useMemo(() => {
    return z.object({
      id: z.string(),
      title: z.string().min(1).max(15, '最多15个字'),
      content: z.string().min(1),
      isPopUp: z.boolean(),
      type: z.string(),
    });
  }, []);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      id: '',
      title: '',
      content: '',
      isPopUp: false,
      type: 'system',
    },
  });

  useEffect(() => {
    if (open && systemNotify) {
      form.setValue('id', systemNotify.id);
      form.setValue('title', systemNotify.title);
      form.setValue('content', systemNotify.content);
      form.setValue('isPopUp', systemNotify.isPopUp);
      form.setValue('type', systemNotify.type);
    }
  }, [form, systemNotify, open]);

  const mutation = useMutation({
    mutationFn: addSystemNotiryConfig,
    onSuccess: () => {
      setOpen(false);
      form.reset();
      queryClient.refetchQueries({
        queryKey: ['systemNotifyList'],
      });
    },
  });

  const editMutation = useMutation({
    mutationFn: setSystemNotifyConfig,
    onSuccess: () => {
      setOpen(false);
      form.reset();
      queryClient.refetchQueries({
        queryKey: ['systemNotifyList'],
      });
    },
  });

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    if (values.id) {
      editMutation.mutate({
        id: values.id,
        title: values.title,
        content: values.content,
        isPopUp: values.isPopUp,
      });
    } else {
      mutation.mutate({
        title: values.title,
        content: values.content,
        isPopUp: values.isPopUp,
      });
    }
  };
  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <DialogHeader>
              <DialogTitle>
                {systemNotify?.id ? '修改' : '添加'}系统通知
              </DialogTitle>
              <VisuallyHidden>
                <DialogDescription />
              </VisuallyHidden>
            </DialogHeader>

            <div className="flex flex-col mt-6 mb-6">
              <FormField
                control={form.control}
                name="title"
                render={({ field }) => (
                  <FormItem className="flex items-center gap-1 mb-4 mt-4 space-y-0">
                    <FormLabel className="w-50 flex-shrink-0 text-right mr-2">
                      标题:
                    </FormLabel>
                    <FormControl>
                      <Input
                        type="text"
                        {...field}
                        className="flex-grow"
                        placeholder="填写标题"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="content"
                render={({ field }) => (
                  <FormItem className="flex items-center gap-1 mb-4 mt-4 space-y-0">
                    <FormLabel className="w-50 flex-shrink-0 text-right mr-2">
                      内容：
                    </FormLabel>
                    <FormControl>
                      <Textarea
                        {...field}
                        className="flex-grow"
                        placeholder="填写内容"
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="isPopUp"
                render={({ field }) => (
                  <FormItem className="flex items-center gap-1 mb-4 mt-4 space-y-0">
                    <FormLabel className="w-50 flex-shrink-0 text-right mr-2">
                      是否弹出：
                    </FormLabel>
                    <FormControl>
                      <div>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                        <FormMessage />
                      </div>
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>

            <DialogFooter>
              <Button
                onClick={() => {
                  form.reset();
                  setOpen(false);
                }}
                variant="ghost"
                type="reset"
              >
                取消
              </Button>
              <LoadingButton
                isPending={mutation.isPending}
                disabled={mutation.isPending}
                type="submit"
              >
                保存
              </LoadingButton>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
