import { ColumnDef } from '@tanstack/react-table';
import { Button } from '@mono/ui/button';
import { SystemNotifyDto } from '@/types/systemNotify.ts';
import { formatDate } from '@mono/utils/day';

export function getTableColumns(
  handleSetting: (data: SystemNotifyDto, method: string) => void
): Array<ColumnDef<SystemNotifyDto>> {
  return [
    {
      header: '标题',
      accessorKey: 'title',
      cell: ({ row }) => {
        const { title } = row.original;
        return <span>{title}</span>;
      },
    },
    {
      header: '内容',
      accessorKey: 'content',
      cell: ({ row }) => {
        const { content } = row.original;
        return <span>{content}</span>;
      },
    },
    {
      header: '是否弹窗',
      accessorKey: 'isPopUp',
      cell: ({ row }) => {
        const { isPopUp } = row.original;
        return <span>{isPopUp ? '是' : '否'}</span>;
      },
    },
    {
      header: '消息类型',
      accessorKey: 'createdAt',
      cell: ({ row }) => {
        const { createdAt } = row.original;
        return <span>{formatDate(createdAt)}</span>;
      },
    },

    {
      header: '操作',
      accessorKey: 'action',
      cell: ({ row }) => {
        const ad = row.original;
        return (
          <>
            <Button variant="ghost" onClick={() => handleSetting(ad, 'edit')}>
              编辑
            </Button>
            <Button variant="ghost" onClick={() => handleSetting(ad, 'delete')}>
              删除
            </Button>
          </>
        );
      },
    },
  ];
}
