import { ColumnDef, createColumnHelper } from '@tanstack/react-table';
import { formatDate } from '@mono/utils/day';
import { Channel } from '@/types/channel';
import DropdownMenuComponent from '../DropdownMenuComponent';
import { DropdownMenuItem } from '@mono/ui/dropdown-menu';
import { toast } from 'sonner';
import { Switch } from '@mono/ui/switch';
import { Link } from '@tanstack/react-router';
import { Button } from '@mono/ui/button';

const columnHelper = createColumnHelper<Channel>();

export const getColumns = (
  onDelete: (item: Channel) => void,
  onEdit: (item: Channel) => void,
  onEnable: (item: Channel, status: boolean) => void
): ColumnDef<Channel, never>[] => {
  return [
    columnHelper.accessor('channelName', {
      cell: (info) => (
        <div className="flex items-center gap-2">
          <span>{info.getValue()}</span>
        </div>
      ),
      header: () => <span>渠道名称</span>,
    }),
    columnHelper.accessor('channelCode', {
      cell: (info) => info.getValue(),
      header: () => <span>渠道码</span>,
    }),
    columnHelper.accessor('createdAt', {
      cell: (info) => (
        <span className="text-muted-foreground">
          {formatDate(info.getValue())}
        </span>
      ),
      header: () => <span>创建时间</span>,
    }),
    columnHelper.accessor('userCount', {
      cell: (info) => (
        <Link
          to="/clientUser"
          search={{ channelCode: info.row.original.channelCode }}
        >
          <Button variant="link">{info.getValue()}</Button>
        </Link>
      ),
      header: () => <span>用户数</span>,
    }),
    columnHelper.accessor('orderCount', {
      cell: (info) => (
        <Link
          to="/order"
          search={{ channelCode: info.row.original.channelCode }}
        >
          <Button variant="link">{info.getValue()}</Button>
        </Link>
      ),
      header: () => <span>订单数</span>,
    }),

    columnHelper.accessor('orderTotalAmount', {
      header: () => '下单总金额',
      cell: (info) => (
        <span className="text-muted-foreground">{info.getValue()}</span>
      ),
    }),
    columnHelper.accessor('enabled', {
      cell: (info) => (
        <Switch
          checked={info.getValue()}
          onCheckedChange={(value) => onEnable(info.row.original, value)}
        />
      ),
      header: () => <span>状态</span>,
    }),
    columnHelper.accessor('id', {
      cell: ({ row }) => (
        <DropdownMenuComponent>
          <DropdownMenuItem
            onClick={() => {
              navigator.clipboard.writeText(
                `${import.meta.env.VITE_APP_OFFICIAL_URL}?channel=${row.original.channelCode}`
              );
              toast.success('复制成功');
            }}
          >
            复制链接
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => onEdit(row.original)}>
            编辑
          </DropdownMenuItem>
          <DropdownMenuItem
            onClick={() => {
              onDelete(row.original);
            }}
            className="text-destructive focus:text-destructive"
          >
            删除
          </DropdownMenuItem>
        </DropdownMenuComponent>
      ),

      header: () => <span>操作</span>,
    }),
  ];
};
