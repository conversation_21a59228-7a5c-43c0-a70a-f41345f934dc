import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON>alogHeader,
  DialogTitle,
} from '@mono/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from '@mono/ui/form';
import { VisuallyHidden } from '@radix-ui/react-visually-hidden';
import { Input } from '@mono/ui/input';

import { Button } from '@mono/ui/button';
import { LoadingButton } from '@/components/loading-button.tsx';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useMutation, useQuery } from '@tanstack/react-query';
import { createScript, getScriptList } from '@/api/script.ts';

export function ScriptUploadModal({
  open,
  setOpen,
  onSuccess,
  ScriptType,
}: {
  open: boolean;
  setOpen: (open: boolean) => void;
  onSuccess: () => void;
  ScriptType: 'rpa' | 'spider';
}) {
  const data = useQuery({
    queryKey: ['getScriptList', ScriptType],
    queryFn: () =>
      getScriptList(
        {
          page: 1,
          size: 1,
        },
        ScriptType
      ),
    enabled: open,
  });

  const formSchema = z.object({
    scripts: z.string(),
    version: z.string().regex(/^(\d|v)+(\.\d+)*$/),
  });

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      scripts: '',
      version: '',
    },
  });

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) {
      return;
    }

    const reader = new FileReader();
    reader.onloadend = () => {
      const base64String = reader.result?.toString() ?? '';
      form.setValue('scripts', base64String.split(',')[1]);
      form.trigger('scripts').then();
    };
    reader.readAsDataURL(file);
  };

  const mutation = useMutation({
    mutationFn: (values: z.infer<typeof formSchema>) => {
      return createScript({ type: ScriptType, ...values });
    },
    onSuccess: () => {
      form.reset();
      setOpen(false);
      onSuccess();
    },
  });

  const onSubmit = (values: z.infer<typeof formSchema>) => {
    if (!values.scripts) {
      form.setError('scripts', {
        type: 'custom',
        message: '',
      });
      return;
    }

    mutation.mutate(values);
  };

  const onCancel = () => {
    form.reset();
    setOpen(false);
  };

  return (
    <>
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)}>
              <DialogHeader>
                <DialogTitle>
                  {ScriptType === 'spider' ? '爬虫脚本更新' : '浏览器脚本更新'}
                </DialogTitle>

                <VisuallyHidden>
                  <DialogDescription />
                </VisuallyHidden>
              </DialogHeader>

              <div className="flex flex-col mt-6 mb-6">
                <FormField
                  control={form.control}
                  name="scripts"
                  render={() => (
                    <FormItem className="flex items-center gap-1 mb-4 space-y-0">
                      <FormLabel className="w-16 flex-shrink-0 text-right mr-2">
                        脚本文件:
                      </FormLabel>
                      <FormControl>
                        <Input
                          type="file"
                          accept={'.js'}
                          className="flex-grow"
                          onChange={(e) => handleFileChange(e)}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="version"
                  render={({ field }) => (
                    <FormItem className="flex items-center gap-1 mb-4 mt-4 space-y-0">
                      <FormLabel className="w-16 flex-shrink-0 text-right mr-2">
                        版本号:
                      </FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          className="flex-grow"
                          autoComplete="off"
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />

                <span className={'text-sm pl-[78px]'}>
                  当前版本：{data.data?.data?.[0]?.version}
                </span>
              </div>

              <DialogFooter>
                <Button onClick={() => onCancel()} variant="ghost" type="reset">
                  取消
                </Button>
                <LoadingButton type="submit">确认发布</LoadingButton>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </>
  );
}
