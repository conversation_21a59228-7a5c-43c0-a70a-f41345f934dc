import { UserLoginOutput } from '@/types/user';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@mono/ui/dialog';
import { InputOTP, InputOTPGroup, InputOTPSlot } from '@mono/ui/input-otp';
import { useMemo } from 'react';

const REGEXP_ONLY_DIGITS = '^\\d+$';

export function LoginAuthenticator({
  open,
  setOpen,
  onComplete,
  data,
}: {
  open: boolean;
  setOpen: (open: boolean) => void;
  onComplete: (token: string) => void;
  data: UserLoginOutput;
}) {
  const showQrCode = useMemo(() => {
    if (!data) {
      return false;
    }
    return data.initMfa;
  }, [data]);

  const showDigitalCode = useMemo(() => {
    if (!data) {
      return false;
    }
    return data.verifyMfa;
  }, [data]);

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent
        className="sm:max-w-[425px]"
        onInteractOutside={(e) => e.preventDefault()}
      >
        <DialogHeader>
          <DialogTitle>请输入6位数字动态码</DialogTitle>
        </DialogHeader>
        <div className="flex flex-col items-center justify-center gap-4">
          {showQrCode && (
            <img src={data?.qrcode} alt="" className="w-40 h-40" />
          )}
          {showDigitalCode && (
            <InputOTP
              maxLength={6}
              pattern={REGEXP_ONLY_DIGITS}
              onComplete={(value) => {
                onComplete(value);
              }}
            >
              <InputOTPGroup>
                <InputOTPSlot index={0} />
                <InputOTPSlot index={1} />
                <InputOTPSlot index={2} />
                <InputOTPSlot index={3} />
                <InputOTPSlot index={4} />
                <InputOTPSlot index={5} />
              </InputOTPGroup>
            </InputOTP>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
