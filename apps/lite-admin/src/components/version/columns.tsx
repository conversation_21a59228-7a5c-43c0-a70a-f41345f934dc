import { ColumnDef } from '@tanstack/react-table';
import { formatDate } from '@mono/utils/day';
import { Desktop } from '@/types/desktopVersion';
import { Button } from '@mono/ui/button';

export function getTableColumns(
  handleSetting: (data: Desktop, method: string) => void
): Array<ColumnDef<Desktop>> {
  return [
    {
      header: '序列号',
      accessorKey: 'index',
      cell: ({ row }) => {
        return <span>{row.index + 1}</span>;
      },
    },
    {
      header: '版本号',
      accessorKey: 'version',
      cell: ({ row }) => {
        const { version } = row.original;
        return <span>{version}</span>;
      },
    },
    {
      header: '发布人',
      accessorKey: 'publishName',
      cell: ({ row }) => {
        const { publishName } = row.original;
        return <span>{publishName}</span>;
      },
    },
    {
      header: '版本公告',
      accessorKey: 'notice',
      cell: ({ row }) => {
        const { notice } = row.original;
        return <span>{notice}</span>;
      },
    },
    {
      header: '设备',
      accessorKey: 'type',
      cell: ({ row }) => {
        const { type } = row.original;
        return <span>{type}</span>;
      },
    },
    {
      header: '发布时间',
      accessorKey: 'createdAt',
      cell: ({ row }) => {
        const { createdAt } = row.original;
        return (
          <span className="text-muted-foreground">{formatDate(createdAt)}</span>
        );
      },
    },
    {
      header: '操作',
      accessorKey: 'action',
      cell: ({ row }) => {
        const desktop = row.original;
        return (
          <>
            <Button
              variant="ghost"
              onClick={() => handleSetting(desktop, 'edit')}
            >
              修改
            </Button>
            <Button
              variant="ghost"
              onClick={() => handleSetting(desktop, 'delete')}
            >
              删除
            </Button>
          </>
        );
      },
    },
  ];
}
