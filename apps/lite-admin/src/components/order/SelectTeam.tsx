'use client';
import * as React from 'react';
import { CheckIcon } from '@radix-ui/react-icons';
import { cn } from '@/lib/utils';
import { Button } from '@mono/ui/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@mono/ui/command';
import { Popover, PopoverContent, PopoverTrigger } from '@mono/ui/popover';
import { useQuery } from '@tanstack/react-query';
import { teamList } from '@/api/team.ts';
import { useMemo } from 'react';
import { debounce } from 'lodash';
import { LoadingContainer } from '@/components/loading.tsx';
import { Badge } from '@mono/ui/badge';
import { Team } from '@/types/team';

export function SelectTeam({
  value,
  onChange,
}: {
  value: string;
  onChange: (value?: Team) => void;
}) {
  const [open, setOpen] = React.useState(false);
  const [searchText, setSearchText] = React.useState('');
  const teamsQuery = useQuery({
    queryFn: () => teamList({ teamName: searchText }),
    queryKey: ['getTeamOrder', searchText],
    enabled: !!searchText,
  });

  // TODO: 优化搜索
  const searchFun = useMemo(
    () => debounce(setSearchText, 300),
    [setSearchText]
  );

  const currentTeam = useMemo(() => {
    if (value) {
      return teamsQuery.data?.data?.find((team) => team.id === value);
    }
    return undefined;
  }, [teamsQuery.data, value]);

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button variant="outline" className="w-full justify-start">
          {currentTeam ?
            <>{currentTeam.name}</>
          : <>选择团队</>}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="p-0 w-[532px]">
        <Command shouldFilter={false}>
          <CommandInput
            onChangeCapture={(e: React.ChangeEvent<HTMLInputElement>) => {
              searchFun(e.target.value);
            }}
            placeholder="请输入团队ID进行搜索"
            className="h-9"
          />
          <CommandList>
            {teamsQuery.isLoading ?
              <div className="py-6">
                <LoadingContainer className="w-5 h-5" />
              </div>
            : <CommandEmpty>No teams found</CommandEmpty>}
            <CommandGroup>
              {teamsQuery.data &&
                teamsQuery.data.data?.map((team) => (
                  <CommandItem
                    // disabled={team.hasPendingOrder || team.hasVip}
                    key={team.id}
                    value={team.id}
                    className="w-full flex items-center gap-2 justify-between"
                    onSelect={(currentValue) => {
                      onChange(currentValue === value ? undefined : team);
                      setOpen(false);
                    }}
                  >
                    <span>{team.name}</span>
                    {team.isVip && <Badge>VIP</Badge>}
                    <CheckIcon
                      className={cn(
                        'ml-auto h-4 w-4',
                        value === `${team.id}` ? 'opacity-100' : 'opacity-0'
                      )}
                    />
                  </CommandItem>
                ))}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
