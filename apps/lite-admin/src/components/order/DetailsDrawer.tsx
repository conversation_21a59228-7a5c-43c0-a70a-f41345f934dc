import {
  Sheet,
  She<PERSON><PERSON>ontent,
  She<PERSON><PERSON>eader,
  SheetTitle,
  SheetDescription,
} from '@mono/ui/sheet';
import { useQuery } from '@tanstack/react-query';
import { orderDetail } from '@/api/order.ts';
import {
  OrderSourceMap,
  OrderStatusMap,
  OrderTypeMap,
  PayTypeMap,
} from '@/types/order.ts';
import { format } from 'date-fns';

interface RefundRecordProps {
  orderId: string;
  open: boolean;
  onChange: (open: boolean) => void;
}
export function DetailsDrawer({ open, orderId, onChange }: RefundRecordProps) {
  const query = useQuery({
    queryKey: ['orderDetail', orderId],
    queryFn: () => {
      return orderDetail(orderId);
    },
  });
  if (!query.data) {
    return null;
  }
  return (
    <>
      <Sheet open={open} onOpenChange={onChange}>
        <SheetContent className="w-[740px] !max-w-none">
          <SheetHeader>
            <SheetTitle>
              订单详情 -{OrderTypeMap.getType(query.data?.orderType)}
            </SheetTitle>
            <SheetDescription></SheetDescription>
          </SheetHeader>
          <div
            className="overflow-auto flex flex-col gap-4"
            style={{ height: 'calc(100% - 32px)' }}
          >
            <div>
              <div className={'font-bold mb-2'}>商品信息</div>
              <div
                className={
                  'border rounded-sm p-4 flex flex-row gap-4 justify-between'
                }
              >
                <div className={'bg-muted p-6 rounded-md text-center'}>
                  <div>￥{query.data?.payAmount}</div>
                  <div className={'text-sm'}>实付金额</div>
                </div>
                <div className={'bg-muted p-6 rounded-md text-center'}>
                  <div>{query.data?.interestCount}</div>
                  <div className={'text-sm'}>权益包</div>
                </div>

                {query.data?.orderType !== 'upgrade' && (
                  <div className={'bg-muted p-6 rounded-md text-center'}>
                    <div>{query.data?.vipMonth}</div>
                    <div className={'text-sm'}>购买月份</div>
                  </div>
                )}

                <div className={'bg-muted p-6 rounded-md text-center'}>
                  <div>{query.data?.freeMonth}</div>
                  <div className={'text-sm'}>赠送月份</div>
                </div>
                <div className={'bg-muted p-6 rounded-md text-center'}>
                  <div>{query.data?.days}</div>
                  <div className={'text-sm'}>赠送天数</div>
                </div>
              </div>
            </div>

            <div>
              <div className={'font-bold mb-2'}>订单信息</div>
              <div className="flex flex-col gap-3.5 border rounded-sm p-4">
                <div>
                  <span>订单号</span>：{query.data?.orderNo}
                </div>
                <div>
                  <span>团队名称</span>：{query.data?.teamName}
                </div>
                <div>
                  <span>团队ID</span>：{query.data?.code}
                </div>
                <div>
                  <span>订单类型</span>：
                  {OrderTypeMap.getType(query.data?.orderType)}
                </div>
                <div>
                  <span>创建时间</span>：
                  {query.data?.createdAt ?
                    format(
                      new Date(query.data?.createdAt),
                      'yyyy-MM-dd HH:mm:ss'
                    )
                  : '-'}
                </div>
                <div>
                  <span>订单状态</span>：
                  {OrderStatusMap.getStatus(query.data?.orderStatus)}
                </div>
                <div>
                  <span>订单金额</span>：￥{query.data?.totalAmount}
                </div>
                <div>
                  <span>应付金额</span>：￥{query.data?.payableAmount}
                </div>
                <div>
                  <span>支付时间</span>：
                  {query.data?.payTime ?
                    format(new Date(query.data?.payTime), 'yyyy-MM-dd HH:mm:ss')
                  : '-'}
                </div>
                <div>
                  <span>到期日期</span>：
                  {query.data?.expiredAt ?
                    format(
                      new Date(query.data?.expiredAt),
                      'yyyy-MM-dd HH:mm:ss'
                    )
                  : '-'}
                </div>
                <div>
                  <span>支付方式</span>：
                  {PayTypeMap.getType(query.data?.payType) || '-'}
                </div>

                <div>
                  <span>订单来源</span>：
                  {OrderSourceMap[query.data?.orderSource] || '-'}
                </div>

                <div>
                  <span>创建人</span>：{query.data?.creatorName || '-'}
                </div>
                <div>
                  <span>备注</span>：{query.data?.remark || '-'}
                </div>
              </div>
            </div>
          </div>
        </SheetContent>
      </Sheet>
    </>
  );
}
