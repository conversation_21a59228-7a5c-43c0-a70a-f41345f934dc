import { Input } from '@mono/ui/input';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@mono/ui/select';
import { Button } from '@mono/ui/button';
import { useEffect, useMemo, useState } from 'react';
import { DataTable } from '@/components/dataTable';
import { ColumnDef, PaginationState } from '@tanstack/react-table';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@mono/ui/alert-dialog';
import { CorporateModal } from '@/components/order/CorporateModal.tsx';
import { DetailsDrawer } from '@/components/order/DetailsDrawer.tsx';
import { useQuery } from '@tanstack/react-query';
import { cancelOrder, orderList } from '@/api/order.ts';
import {
  Order,
  OrderStatusMap,
  OrderTypeMap,
  PayTypeMap,
} from '@/types/order.ts';
import { format } from 'date-fns';
import { useImmer } from 'use-immer';
import DropdownMenuComponent from '../DropdownMenuComponent';
import { DropdownMenuItem } from '@mono/ui/dropdown-menu';
import { Link, useSearch } from '@tanstack/react-router';
import { CalendarRange } from '@mono/ui/common/CalendarRange';
import { salesTypeMap } from '@/constants/salesTypeMap.ts';
import { SelectService } from '../SelectService';
import { useCalendarRangeProps } from '@mono/hooks';
import { CreateOrder } from './CreateOrder';

export interface OrderFilter {
  orderNo: string;
  teamName: string;
  phone: string;
  orderStatus: string;
  orderType: string;
  payType: string;
  createStartTime?: number;
  createEndTime?: number;
  payStartTime?: number;
  payEndTime?: number;
  channelCode?: string;
  salesType?: number;
  customerId?: string;
}

interface FilterProps {
  onSearchFilter: (filter: OrderFilter) => void;
  values: OrderFilter;
}

const FilterBar = ({ onSearchFilter, values }: FilterProps) => {
  const [filter, setFilter] = useImmer<OrderFilter>(values);

  const [createRange, setCreateRange] = useCalendarRangeProps(
    { from: filter.createStartTime, to: filter.createEndTime },
    (value) => {
      setFilter((draft) => {
        draft.createStartTime = value?.from;
        draft.createEndTime = value?.to;
      });
    }
  );

  const [payRange, setPayRange] = useCalendarRangeProps(
    { from: filter.payStartTime, to: filter.payEndTime },
    (value) => {
      setFilter((draft) => {
        draft.payStartTime = value?.from;
        draft.payEndTime = value?.to;
      });
    }
  );

  useEffect(() => {
    if (filter.orderStatus === 'all') {
      setFilter((draft) => {
        draft.orderStatus = '';
      });
    }
    if (filter.orderType === 'all') {
      setFilter((draft) => {
        draft.orderType = '';
      });
    }
    if (filter.payType === 'all') {
      setFilter((draft) => {
        draft.payType = '';
      });
    }
  }, [filter, setFilter]);

  const onSubmit = () => {
    onSearchFilter(filter);
  };

  return (
    <div className="flex flex-wrap gap-4 h-fit p-1">
      <div>
        <Input
          placeholder="请输入订单号"
          value={filter.orderNo}
          onChange={(e) => {
            setFilter((draft) => {
              draft.orderNo = e.target.value;
            });
          }}
        />
      </div>
      <div>
        <Input
          placeholder="请输入团队名称/ID"
          value={filter.teamName}
          onChange={(e) => {
            setFilter((draft) => {
              draft.teamName = e.target.value;
            });
          }}
        />
      </div>
      <div>
        <Input
          placeholder="请输入手机号"
          value={filter.phone}
          onChange={(e) => {
            setFilter((draft) => {
              draft.phone = e.target.value;
            });
          }}
        />
      </div>
      <div className="flex items-center gap-2  whitespace-nowrap">
        订单状态:
        <Select
          onValueChange={(value) => {
            setFilter((draft) => {
              draft.orderStatus = value;
            });
          }}
          value={filter.orderStatus || 'all'}
        >
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="请选择订单状态" />
          </SelectTrigger>
          <SelectContent>
            <SelectGroup>
              <SelectItem value="all">全部</SelectItem>
              <SelectItem value="pending">待支付</SelectItem>
              <SelectItem value="paid">已支付</SelectItem>
              <SelectItem value="cancelled">已取消</SelectItem>
              <SelectItem value="refunded">已退费</SelectItem>
            </SelectGroup>
          </SelectContent>
        </Select>
      </div>
      <div className="flex items-center gap-2  whitespace-nowrap">
        订单类型:
        <Select
          defaultValue="all"
          onValueChange={(value) => {
            setFilter((draft) => {
              draft.orderType = value;
            });
          }}
        >
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="请选择订单类型" />
          </SelectTrigger>
          <SelectContent>
            <SelectGroup>
              <SelectItem value="all">全部</SelectItem>
              <SelectItem value="create">开通</SelectItem>
              <SelectItem value="renew">续费</SelectItem>
              <SelectItem value="upgrade">升级</SelectItem>
              <SelectItem value="gift">赠送</SelectItem>
            </SelectGroup>
          </SelectContent>
        </Select>
      </div>
      <div className="flex items-center gap-2  whitespace-nowrap">
        支付方式:
        <Select
          defaultValue="all"
          onValueChange={(value) => {
            setFilter((draft) => {
              draft.payType = value;
            });
          }}
        >
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="请选择支付方式" />
          </SelectTrigger>
          <SelectContent>
            <SelectGroup>
              <SelectItem value="all">全部</SelectItem>
              <SelectItem value="wechatPay">微信</SelectItem>
              <SelectItem value="alipay">支付宝</SelectItem>
              <SelectItem value="corporateTransfer">对公转账</SelectItem>
            </SelectGroup>
          </SelectContent>
        </Select>
      </div>
      <div className="flex items-center gap-2 h-fit whitespace-nowrap">
        创建时间:
        <CalendarRange value={createRange} onChange={setCreateRange} />
      </div>
      <div className="flex items-center gap-2 h-fit whitespace-nowrap">
        付款时间:
        <CalendarRange value={payRange} onChange={setPayRange} />
      </div>
      <div>
        <Input
          placeholder="搜索渠道码"
          value={filter.channelCode}
          onChange={(e) => {
            setFilter((draft) => {
              draft.channelCode = e.target.value;
            });
          }}
        />
      </div>
      <div>
        <SelectService
          value={filter.customerId}
          onChange={(value) => {
            setFilter((draft) => {
              draft.customerId = value;
            });
          }}
        />
      </div>
      <div className="flex items-center gap-2 h-fit whitespace-nowrap">
        销售类型:
        <Select
          value={
            filter.salesType !== undefined ? String(filter.salesType) : 'all'
          }
          onValueChange={(value) => {
            setFilter((draft) => {
              draft.salesType = value === 'all' ? undefined : parseInt(value);
            });
          }}
        >
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="请选择销售类型" />
          </SelectTrigger>
          <SelectContent>
            <SelectGroup>
              <SelectItem value="all">全部</SelectItem>
              <SelectItem value="0">未购买</SelectItem>
              <SelectItem value="1">新购</SelectItem>
              <SelectItem value="2">复购</SelectItem>
              <SelectItem value="4">已购买</SelectItem>
            </SelectGroup>
          </SelectContent>
        </Select>
      </div>
      <Button onClick={() => onSubmit()}>搜索</Button>
      <CreateOrder />
    </div>
  );
};

export function OrderPage() {
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  });
  const {
    channelCode,
    salesType,
    createEndTime,
    createStartTime,
    customerId,
    orderStatus,
  } = useSearch({
    strict: false,
  });
  const [dialog, setDialog] = useState(false);

  const [corporateModalOpen, setCorporateModalOpen] = useState(false);
  const [detailsDrawerOpen, setDetailsDrawerOpen] = useState(false);
  const [filter, setFilter] = useImmer<OrderFilter>({
    orderNo: '',
    teamName: '',
    phone: '',
    orderStatus: orderStatus ?? '',
    orderType: '',
    payType: '',
    createStartTime,
    createEndTime,
    channelCode,
    salesType,
    customerId,
  });

  const [orderId, setOrderId] = useState('');

  const columns = useMemo<Array<ColumnDef<Order>>>(() => {
    return [
      {
        header: '订单号',
        accessorKey: 'orderNo',
        cell: ({ row }) => {
          const { orderNo } = row.original;
          return <span>{orderNo}</span>;
        },
      },
      {
        header: '团队名称',
        accessorKey: 'teamName',
        cell: ({ row }) => {
          const { teamName, code } = row.original;
          return (
            <div className="w-44">
              <div className="line-clamp-1">{teamName}</div>
              <Link to="/team" search={{ teamCode: code }}>
                <Button className="p-0 h-5" variant="link">
                  {code}
                </Button>
              </Link>
            </div>
          );
        },
      },
      {
        header: '手机号',
        accessorKey: 'phone',
        cell: ({ row }) => {
          const { phone } = row.original;
          return <span className="text-muted-foreground">{phone}</span>;
        },
      },
      {
        header: '订单状态',
        accessorKey: 'status',
        cell: ({ row }) => {
          const { orderStatus } = row.original;
          return (
            <span className="text-muted-foreground">
              {OrderStatusMap.getStatus(orderStatus)}
            </span>
          );
        },
      },
      {
        header: '订单类型',
        accessorKey: 'orderStatus',
        cell: ({ row }) => {
          const { orderType } = row.original;
          return (
            <span className="text-muted-foreground">
              {OrderTypeMap.getType(orderType)}
            </span>
          );
        },
      },
      {
        header: '销售类型',
        accessorKey: 'salesType',
        cell: ({ row }) => {
          const { salesType } = row.original;
          return (
            <span className="text-muted-foreground">
              {salesType === undefined ?
                '-'
              : salesTypeMap[salesType.toString() as keyof typeof salesTypeMap]}
            </span>
          );
        },
      },
      {
        header: '支付方式',
        accessorKey: 'payType',
        cell: ({ row }) => {
          const { payType } = row.original;
          return (
            <span className="text-muted-foreground">
              {PayTypeMap.getType(payType) || '-'}
            </span>
          );
        },
      },
      {
        header: '实付金额',
        accessorKey: 'payAmount',
        cell: ({ row }) => {
          const { payAmount } = row.original;
          return <span className="text-muted-foreground">{payAmount}</span>;
        },
      },
      {
        header: '创建时间',
        accessorKey: 'createdAt',
        cell: ({ row }) => {
          const { createdAt } = row.original;
          return (
            <span className="text-muted-foreground">
              {createdAt ? format(createdAt, 'yyyy-MM-dd HH:mm:ss') : '-'}
            </span>
          );
        },
      },
      {
        header: '支付时间',
        accessorKey: 'payTime',
        cell: ({ row }) => {
          const { payTime } = row.original;
          return (
            <span className="text-muted-foreground">
              {payTime ? format(payTime, 'yyyy-MM-dd HH:mm:ss') : '-'}
            </span>
          );
        },
      },
      {
        header: '归属',
        accessorKey: 'customerName',
        cell: ({ row }) => {
          const { customerName } = row.original;
          return (
            <span className="text-muted-foreground">{customerName || '-'}</span>
          );
        },
      },
      {
        header: '操作',
        accessorKey: 'action',
        cell: ({ row }) => {
          return (
            <>
              <DropdownMenuComponent>
                <DropdownMenuItem
                  onClick={() => {
                    setOrderId(row.original.orderNo);
                    setDetailsDrawerOpen(true);
                  }}
                >
                  详情
                </DropdownMenuItem>
                {row.original.orderStatus !== 'paid' && (
                  <DropdownMenuItem
                    onClick={() => {
                      setOrderId(row.original.orderNo);
                      setCorporateModalOpen(true);
                    }}
                  >
                    开通对公转账
                  </DropdownMenuItem>
                )}
                {row.original.orderStatus === 'pending' && (
                  <DropdownMenuItem
                    onClick={() => {
                      setOrderId(row.original.orderNo);
                      setDialog(true);
                    }}
                  >
                    取消订单
                  </DropdownMenuItem>
                )}
              </DropdownMenuComponent>
            </>
          );
        },
      },
    ];
  }, []);

  const query = useQuery({
    queryKey: ['orderList', filter, pagination],
    queryFn: () => {
      return orderList({
        page: pagination.pageIndex + 1,
        size: pagination.pageSize,
        ...filter,
      });
    },
  });

  const handelDelete = async () => {
    await cancelOrder(orderId);
    query.refetch();
  };

  return (
    <>
      <div className="flex flex-col w-full h-full overflow-hidden">
        <FilterBar
          onSearchFilter={(_filter) => {
            setFilter(_filter);
          }}
          values={filter}
        />
        <div className={'py-2'}>总金额：￥{query.data?.totalAmount}</div>
        <DataTable
          columns={columns}
          data={query.data?.data}
          rowCount={query.data?.totalSize}
          pagination={pagination}
          setPagination={setPagination}
        />
      </div>
      {corporateModalOpen && (
        <CorporateModal
          orderId={orderId}
          open={corporateModalOpen}
          onChange={setCorporateModalOpen}
          onSuccess={() => {
            query.refetch();
          }}
        />
      )}
      {detailsDrawerOpen && (
        <DetailsDrawer
          orderId={orderId}
          open={detailsDrawerOpen}
          onChange={setDetailsDrawerOpen}
        />
      )}

      <AlertDialog open={dialog} onOpenChange={setDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确定取消订单吗?</AlertDialogTitle>
            <AlertDialogDescription></AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction onClick={() => handelDelete()}>
              确定
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
