import { Team } from '@/types/team.ts';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@mono/ui/dialog';

import { Button } from '@mono/ui/button';
import { useMutation, useQuery } from '@tanstack/react-query';
import {
  getProxyList,
  getTeamProxyList,
  updateProxyList,
} from '@/api/proxy.ts';
import {
  CascaderSelect,
  CategoryModelBase,
} from '@/components/CascaderSelect.tsx';
import { useEffect, useState } from 'react';
import { X } from 'lucide-react';
import { LoadingButton } from '@/components/loading-button.tsx';
import { toast } from 'sonner';
import { IProxyType } from '@/types/proxyType.ts';

interface WebSettingProps {
  team: Team;
  open: boolean;
  onChange: (open: boolean) => void;
}

export function WebSetting({ team, open, onChange }: WebSettingProps) {
  const [proxyTags, setProxyTags] = useState<Array<CategoryModelBase<never>>>(
    []
  );

  const [showSelect, setShowSelect] = useState(false);

  const query = useQuery({
    queryKey: ['getProxyList'],
    queryFn: () => getProxyList(),
  });

  const formatData = (data: IProxyType[]) => {
    return data.map((item) => {
      return {
        id: item.code,
        text: item.name,
        children: item.cities?.map((child) => {
          return {
            id: child.code,
            text: child.name,
          };
        }),
      };
    });
  };

  const onAddTag = (value: CategoryModelBase<never>[]) => {
    setProxyTags((prev) => {
      const newTags = [...prev];
      value.forEach((item) => {
        const isExist = prev.some((tag) =>
          tag?.children?.some((child) => child.id === item.children?.[0]?.id)
        );
        if (!isExist) {
          newTags.push(item);
        }
      });
      return newTags;
    });
  };

  const onRemove = (item: CategoryModelBase<never>) => {
    setProxyTags((prev) => {
      const newTags = [...prev];
      const index = newTags.findIndex((tag) =>
        tag?.children?.some((child) => child.id === item.children?.[0]?.id)
      );
      newTags.splice(index, 1);
      return newTags;
    });
  };

  const mutation = useMutation({
    mutationKey: ['updateProxyList'],
    mutationFn: (data: never) => updateProxyList(team.id, data),
    onSuccess: () => {
      onChange(false);
    },
  });

  const onHandelSubmit = () => {
    if (proxyTags.length === 0) {
      toast.error('请至少添加一个代理区域');
      return;
    }

    const source = proxyTags.map((item) => {
      return {
        name: item.text,
        code: item.id,
        cities: item.children?.map((child) => {
          return {
            name: child.text,
            code: child.id,
          };
        }),
      };
    });
    mutation.mutate(source as never);
  };

  const teamMutation = useMutation({
    mutationKey: ['getTeamProxyList'],
    mutationFn: () => getTeamProxyList(team.id),
    onSuccess: (result) => {
      setProxyTags(formatData(result));
    },
  });

  useEffect(() => {
    teamMutation.mutate();
  }, [team]);
  return (
    <>
      <div className={'fixed inset-0 z-50 bg-black/80'}></div>
      <Dialog open={open} onOpenChange={onChange} modal={false}>
        <DialogContent
          className="max-w-[620px]"
          onOpenAutoFocus={(e) => e.preventDefault()}
          onInteractOutside={(e) => e.preventDefault()}
        >
          <DialogHeader>
            <DialogTitle className={'mb-4'}>代理设置</DialogTitle>
            <div className={'flex flex-wrap items-center gap-3'}>
              {proxyTags.map((item, index) => (
                <div
                  key={index}
                  className={
                    'rounded-sm px-2 py-1 bg-gray-300  flex items-center'
                  }
                >
                  {item.text}
                  {item.children && item.children?.length > 0 && <span>/</span>}
                  {item.children &&
                    item.children?.length > 0 &&
                    item.children[0].text}
                  <X
                    size={16}
                    className={'cursor-pointer'}
                    onClick={() => onRemove(item)}
                  />
                </div>
              ))}
              {showSelect && (
                <CascaderSelect
                  data={formatData(query.data || [])}
                  onChange={(value) => {
                    setShowSelect(false);
                    onAddTag(value);
                  }}
                />
              )}
              {!showSelect && (
                <Button className={'h-8'} onClick={() => setShowSelect(true)}>
                  添加
                </Button>
              )}
            </div>
            <DialogDescription></DialogDescription>

            <DialogFooter className="">
              <Button
                type="button"
                variant="secondary"
                onClick={() => onChange(false)}
              >
                取消
              </Button>

              <LoadingButton
                type="submit"
                variant={'default'}
                onClick={() => onHandelSubmit()}
                isPending={mutation.isPending}
              >
                保存
              </LoadingButton>
            </DialogFooter>
          </DialogHeader>
        </DialogContent>
      </Dialog>
    </>
  );
}
