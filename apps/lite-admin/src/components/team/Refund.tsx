import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON>alogDescription,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON>alogHeader,
  DialogTitle,
} from '@mono/ui/dialog';
import { Team } from '@/types/team.ts';
import { Button } from '@mono/ui/button';
import { useState } from 'react';
import { PaginationState } from '@tanstack/react-table';
import { Input } from '@mono/ui/input';
import { Textarea } from '@mono/ui/textarea';
import { z } from 'zod';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@mono/ui/form';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useMutation, useQuery } from '@tanstack/react-query';
import { refundOrder, refundOrderById } from '@/api/order.ts';
import { RefundParams } from '@/types/order.ts';

interface RefundProps {
  open: boolean;
  onChange: (open: boolean) => void;
  team: Team;
  onSuccess: () => void;
}

export function Refund({ open, onChange, team, onSuccess }: RefundProps) {
  const [pagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  });

  const formSchema = z.object({
    realityPrice: z
      .number({ message: '请输入退费金额' })
      .min(0, '退费金额不能小于0')
      .optional(),
    remark: z.string().optional(),
  });

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      realityPrice: 0,
      remark: undefined,
    },
  });

  const query = useQuery({
    queryKey: ['refundRecord', pagination],
    queryFn: () => refundOrder(team.id),
  });

  const mutation = useMutation({
    mutationFn: (params: RefundParams) => refundOrderById(team.id, params),
    onSuccess: () => {
      onSuccess();
    },
  });

  const onSubmit = (values: z.infer<typeof formSchema>) => {
    const params: RefundParams = {
      realityPrice: Number(values.realityPrice),
      remark: values.remark || '',
    };
    mutation.mutate(params);
  };

  const onHandelSubmit = () => {
    form.handleSubmit(onSubmit)();
  };

  const refundableAmount = query.data?.reduce((pre, cur) => {
    return pre + Number(cur.actualRefundAmount);
  }, 0);

  return (
    <>
      <Dialog open={open} onOpenChange={onChange}>
        <DialogContent
          className="max-w-[620px]"
          onOpenAutoFocus={(e) => e.preventDefault()}
        >
          <DialogHeader>
            <DialogTitle>退费操作</DialogTitle>
            <div className={'pt-2'}>
              <div className={'text-gray-500 py-2'}>
                退费后该团队VIP权益将清空，变为非VIP，团队账号、成员将被冻结
              </div>
              <div>
                <div className="flex justify-between bg-gray-300 p-2 rounded-sm">
                  <div className={'w-[210px]'}>订单号</div>
                  <div className={'w-[70px]'}>付款金额</div>
                  <div className={'w-[82px]'}>可退款金额</div>
                </div>
                {query.data?.map((item) => (
                  <div className="flex justify-between border-b p-1 py-2">
                    <div>{item.orderNo}</div>
                    <div>￥{item.refundAmount}</div>
                    <div>￥{item.actualRefundAmount}</div>
                  </div>
                ))}
              </div>
              <Form {...form}>
                <form
                  onSubmit={form.handleSubmit(onSubmit)}
                  className="w-full space-y-2 pt-4"
                >
                  <FormLabel>应退费：￥{refundableAmount}</FormLabel>
                  <FormField
                    control={form.control}
                    name="realityPrice"
                    render={({ field }) => (
                      <FormItem className={'flex items-center'}>
                        <FormLabel className={'w-[80px] whitespace-nowrap'}>
                          实际退费：
                        </FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            placeholder="请输入退费金额"
                            autoComplete={'off'}
                            value={field.value}
                            onChange={(e) => {
                              const value = e.target.value;
                              console.log(value);
                              if (value === '' || isNaN(Number(value))) {
                                console.log('清空');
                                field.onChange('');
                              } else {
                                field.onChange(Number(value));
                              }
                            }}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="remark"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>备注：</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="请输入(选填)"
                            className="resize-none"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </form>
              </Form>
            </div>
            <DialogDescription></DialogDescription>

            <DialogFooter className="">
              <Button
                type="button"
                variant="secondary"
                onClick={() => onChange(false)}
              >
                取消
              </Button>
              <Button
                type="submit"
                variant={'default'}
                onClick={() => onHandelSubmit()}
              >
                确认退费
              </Button>
            </DialogFooter>
          </DialogHeader>
        </DialogContent>
      </Dialog>
    </>
  );
}
