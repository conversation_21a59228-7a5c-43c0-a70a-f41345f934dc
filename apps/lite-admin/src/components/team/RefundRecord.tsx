import {
  Sheet,
  She<PERSON><PERSON>ontent,
  She<PERSON><PERSON>eader,
  SheetTitle,
  SheetDescription,
} from '@mono/ui/sheet';

import { useQuery } from '@tanstack/react-query';
import { refundRecord } from '@/api/order.ts';
import { format } from 'date-fns';

interface RefundRecordProps {
  teamId: string;
  open: boolean;
  onChange: (open: boolean) => void;
}
export function RefundRecord({ open, onChange, teamId }: RefundRecordProps) {
  const query = useQuery({
    queryKey: ['refundRecord'],
    queryFn: () => refundRecord(teamId),
  });

  return (
    <>
      <Sheet open={open} onOpenChange={onChange}>
        <SheetContent className="w-[640px] !max-w-none">
          <SheetHeader>
            <SheetTitle>退费记录</SheetTitle>
            <SheetDescription></SheetDescription>
          </SheetHeader>
          <div
            className="overflow-auto flex flex-col gap-4"
            style={{ height: 'calc(100% - 32px)' }}
          >
            {query.data?.length === 0 && (
              <div className={'text-center p-8'}>暂无退费记录</div>
            )}
            {query.data?.map((item, index) => (
              <div className={'border rounded-sm p-4'} key={index}>
                <div>退费单：{item.refundNo}</div>

                <div className={'my-4'}>
                  <div className="flex justify-between bg-gray-300 p-2 rounded-sm">
                    <div className={'w-[210px]'}>订单号</div>
                    <div className={'w-[70px]'}>付款金额</div>
                    <div className={'w-[82px]'}>可退款金额</div>
                  </div>
                  {item.orderInfos?.map((ch) => (
                    <div className="flex justify-between border-b py-2">
                      <div className={'w-[210px]'}>{ch.orderNo}</div>
                      <div className={'w-[70px]'}>￥{ch.refundAmount}</div>
                      <div className={'w-[82px]'}>
                        ￥{ch.actualRefundAmount}
                      </div>
                    </div>
                  ))}
                </div>

                <div className={'flex flex-col gap-2'}>
                  <div>应退费：￥{item.refundableAmount}</div>
                  <div>实际退费：￥{item.actualAmount}</div>
                  <div>
                    退费时间：{format(item.createdAt, 'yyyy-MM-dd HH:mm:ss')}
                  </div>
                  <div>退费备注：{item.remark}</div>
                  <div>操作人：{item.creatorName}</div>
                </div>
              </div>
            ))}
          </div>
        </SheetContent>
      </Sheet>
    </>
  );
}
