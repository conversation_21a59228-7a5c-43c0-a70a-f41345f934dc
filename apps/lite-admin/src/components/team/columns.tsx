import { ColumnDef } from '@tanstack/react-table';
import { formatDate } from '@mono/utils/day';
import { Team } from '@/types/team';

import { formatSize } from '@/lib/byte-size';
import DropdownMenuComponent from '../DropdownMenuComponent';
import { DropdownMenuItem } from '@mono/ui/dropdown-menu';
import { TeamAccountsDialog } from './TeamAccountsDialog';
import { Button } from '@mono/ui/button';
import { TeamMembersDialog } from './TeamMembersDialog';
import { Badge } from '@mono/ui/badge';
import { HelpTooltip } from '@mono/ui/common/HelpTooltip';
import { salesTypeMap } from '@/constants/salesTypeMap';

export function getTableColumns(
  onRefund: (data: Team) => void,
  onRefundRecord: (data: Team) => void,
  webSetting: (data: Team) => void
): Array<ColumnDef<Team>> {
  return [
    {
      header: '团队名称',
      accessorKey: 'name',
      cell: ({ row }) => {
        const { name, code } = row.original;
        return (
          <span>
            <div className="line-clamp-1">{name}</div>
            <div className="font-bold">{code}</div>
          </span>
        );
      },
    },

    {
      header: '创建人手机号',
      accessorKey: 'phone',
      cell: ({ row }) => {
        const { phone } = row.original;
        return (
          <span>
            <div>{phone}</div>
          </span>
        );
      },
    },

    {
      header: 'VIP',
      accessorKey: 'enabled',
      cell: ({ row }) => {
        const { expiredAt } = row.original;
        const isExpired = expiredAt && new Date(expiredAt) > new Date(); // 检查 expiredAt 是否为 null 或有效
        return (
          <span>
            {isExpired ?
              <Badge variant="default">已开通</Badge>
            : <Badge variant="secondary">未开通</Badge>}
          </span>
        );
      },
    },
    {
      header() {
        return (
          <span className="flex items-center gap-1">
            账号数
            <HelpTooltip title="查看账号数，不包含网站数" />
          </span>
        );
      },
      accessorKey: 'accountCount',
      cell: ({ row }) => {
        const { accountCount, accountCountLimit, id, code } = row.original;
        return accountCount ?
            <TeamAccountsDialog
              id={id}
              code={code}
              children={
                <Button variant="link" className="px-0 h-4">
                  {accountCount}/{accountCountLimit}
                </Button>
              }
            />
          : <span className="text-muted-foreground">
              {accountCount}/{accountCountLimit}
            </span>;
      },
    },
    {
      header: '成员数',
      accessorKey: 'memberCount',
      cell: ({ row }) => {
        const { memberCount, memberCountLimit, id } = row.original;
        return memberCount ?
            <TeamMembersDialog
              id={id}
              children={
                <Button variant="link" className="px-0 h-4">
                  {memberCount}/{memberCountLimit}
                </Button>
              }
            />
          : <span className="text-muted-foreground">
              {memberCount}/{memberCountLimit}
            </span>;
      },
    },
    {
      header: '素材容量',
      accessorKey: 'capacity',
      cell: ({ row }) => {
        const { usedCapacity, capacity } = row.original;
        return (
          <span>
            {formatSize(usedCapacity)}/{formatSize(capacity)}
          </span>
        );
      },
    },
    {
      header: '创建时间',
      accessorKey: 'createdAt',
      cell: ({ row }) => {
        const { createdAt } = row.original;
        return (
          <span className="text-muted-foreground">{formatDate(createdAt)}</span>
        );
      },
    },
    {
      header: '归属',
      accessorKey: 'customerName',
      cell: ({ row }) => {
        const { customerName } = row.original;
        return (
          <span className="text-muted-foreground">{customerName || '-'}</span>
        );
      },
    },
    {
      header: '销售类型',
      accessorKey: 'salesType',
      cell: ({ row }) => {
        const { salesType } = row.original;
        return (
          <span className="text-muted-foreground">
            {salesType === undefined ?
              '-'
            : salesTypeMap[salesType.toString() as keyof typeof salesTypeMap]}
          </span>
        );
      },
    },
    {
      header: '到期时间',
      accessorKey: 'expiredAt',
      cell: ({ row }) => {
        const { expiredAt } = row.original;
        return (
          <span className="text-muted-foreground">
            {expiredAt ? formatDate(expiredAt) : '-'}
          </span>
        );
      },
    },
    {
      header: '操作',
      accessorKey: 'action',
      cell: ({ row }) => {
        const team = row.original;
        return (
          <DropdownMenuComponent>
            <DropdownMenuItem
              className="text-destructive hover:text-destructive"
              onClick={() => onRefund(team)}
            >
              退费
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => onRefundRecord(team)}>
              退费记录
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => webSetting(team)}>
              代理设置
            </DropdownMenuItem>
          </DropdownMenuComponent>
        );
      },
    },
  ];
}
