import { useForm } from 'react-hook-form';
import { Form, FormField, FormItem, FormLabel } from '@mono/ui/form';
import { Input } from '@mono/ui/input';
import { Button } from '@mono/ui/button';
import { DatePickerWithRange } from '@/components/DatePickerWithRange';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@mono/ui/select';
import {
  ExtendedOpenPlatformOrderParams,
  orderTypeMap,
} from '@/types/openPlatform';
import { Search, RotateCcw } from 'lucide-react';

interface OrderSearchProps {
  onSearch: (params: ExtendedOpenPlatformOrderParams) => void;
  values: Partial<ExtendedOpenPlatformOrderParams>;
}

type SearchForm = {
  orderNo?: string;
  appId?: string;
  code?: string;
  orderType?: 'open_platform_account_points' | 'open_platform_traffic' | '';
  paymentDateRange?: {
    from: Date;
    to: Date;
  };
};

export function OrderSearch({ onSearch, values }: OrderSearchProps) {
  const form = useForm<SearchForm>({
    defaultValues: {
      orderNo: values.orderNo || '',
      appId: values.appId || '',
      code: values.code || '',
      orderType: values.orderType || '',
      paymentDateRange:
        values.paymentStartTime && values.paymentEndTime ?
          {
            from: new Date(values.paymentStartTime),
            to: new Date(values.paymentEndTime),
          }
        : undefined,
    },
  });

  const handleSubmit = (data: SearchForm) => {
    const params: ExtendedOpenPlatformOrderParams = {
      orderNo: data.orderNo || undefined,
      appId: data.appId || undefined,
      code: data.code || undefined,
      orderType: data.orderType || undefined,
      paymentStartTime: data.paymentDateRange?.from?.toISOString(),
      paymentEndTime: data.paymentDateRange?.to?.toISOString(),
    };
    onSearch(params);
  };

  const handleReset = () => {
    form.reset({
      orderNo: '',
      appId: '',
      code: '',
      orderType: '',
      paymentDateRange: undefined,
    });
    onSearch({});
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <FormField
            control={form.control}
            name="orderNo"
            render={({ field }) => (
              <FormItem>
                <FormLabel>订单号</FormLabel>
                <Input {...field} placeholder="请输入订单号" />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="code"
            render={({ field }) => (
              <FormItem>
                <FormLabel>团队Code</FormLabel>
                <Input {...field} placeholder="请输入团队Code" />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="orderType"
            render={({ field }) => (
              <FormItem>
                <FormLabel>购买类型</FormLabel>
                <Select
                  value={field.value !== '' ? String(field.value) : 'all'}
                  onValueChange={(value) =>
                    field.onChange(
                      value === 'all' ? undefined
                      : value === 'open_platform_account_points' ?
                        'open_platform_account_points'
                      : 'open_platform_traffic'
                    )
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="选择订单类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部</SelectItem>
                    <SelectItem value="open_platform_account_points">
                      {orderTypeMap.open_platform_account_points}
                    </SelectItem>
                    <SelectItem value="open_platform_traffic">
                      {orderTypeMap.open_platform_traffic}
                    </SelectItem>
                  </SelectContent>
                </Select>
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="paymentDateRange"
            render={({ field }) => (
              <FormItem>
                <FormLabel>支付时间</FormLabel>
                <DatePickerWithRange
                  onChange={(dateRange) => {
                    field.onChange(
                      dateRange?.from && dateRange?.to ?
                        {
                          from: dateRange.from,
                          to: dateRange.to,
                        }
                      : undefined
                    );
                  }}
                />
              </FormItem>
            )}
          />
        </div>

        <div className="flex gap-2">
          <Button type="submit" className="flex items-center gap-2">
            <Search className="w-4 h-4" />
            搜索
          </Button>
          <Button
            type="button"
            variant="outline"
            onClick={handleReset}
            className="flex items-center gap-2"
          >
            <RotateCcw className="w-4 h-4" />
            重置
          </Button>
        </div>
      </form>
    </Form>
  );
}
