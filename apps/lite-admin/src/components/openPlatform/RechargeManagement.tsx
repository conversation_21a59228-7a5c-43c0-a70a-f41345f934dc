import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { PaginationState } from '@tanstack/react-table';
import { Card, CardContent, CardHeader, CardTitle } from '@mono/ui/card';
import { Button } from '@mono/ui/button';
import { DataTable } from '@/components/dataTable';
import { RechargeSearch } from './RechargeSearch';
import { RechargeDetailDrawer } from './RechargeDetailDrawer';
import { CorporateTransferModal } from './CorporateTransferModal';
import { CreateOrderModal } from './CreateOrderModal';
import { getRechargeColumns } from './RechargeColumns';
import { getRechargeOrders } from '@/api/openPlatform';
import { RechargeOrder, RechargeOrderParams } from '@/types/openPlatform';
import { Plus } from 'lucide-react';

export function RechargeManagement() {
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 20,
  });

  const [searchParams, setSearchParams] = useState<RechargeOrderParams>();

  const [selectedOrder, setSelectedOrder] = useState<RechargeOrder | null>(
    null
  );
  const [detailDrawerOpen, setDetailDrawerOpen] = useState(false);
  const [corporateTransferModalOpen, setCorporateTransferModalOpen] =
    useState(false);
  const [createOrderModalOpen, setCreateOrderModalOpen] = useState(false);

  // 获取充值订单列表
  const { data, isLoading, error } = useQuery({
    queryKey: ['rechargeOrders', pagination, searchParams],
    queryFn: () =>
      getRechargeOrders({
        ...searchParams,
        page: pagination.pageIndex + 1,
        size: pagination.pageSize,
      }),
  });

  // 处理搜索
  const handleSearch = (params: RechargeOrderParams) => {
    setSearchParams({
      ...params,
    });
    setPagination((prev) => ({ ...prev, pageIndex: 0 }));
  };

  // 查看充值订单详情
  const handleViewDetail = (order: RechargeOrder) => {
    setSelectedOrder(order);
    setDetailDrawerOpen(true);
  };

  // 对公转账开通
  const handleCorporateTransfer = (order: RechargeOrder) => {
    setSelectedOrder(order);
    setCorporateTransferModalOpen(true);
  };

  // 关闭详情抽屉
  const handleCloseDetailDrawer = () => {
    setDetailDrawerOpen(false);
    setSelectedOrder(null);
  };

  // 关闭对公转账弹窗
  const handleCloseCorporateTransferModal = () => {
    setCorporateTransferModalOpen(false);
    setSelectedOrder(null);
  };

  const columns = getRechargeColumns({
    onViewDetail: handleViewDetail,
    onCorporateTransfer: handleCorporateTransfer,
  });

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <p className="text-red-500 mb-2">加载失败</p>
          <p className="text-sm text-muted-foreground">
            {error instanceof Error ? error.message : '未知错误'}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">充值管理</h1>
          <p className="text-muted-foreground">
            管理开放平台充值订单信息和对公转账开通
          </p>
        </div>
        <Button
          onClick={() => setCreateOrderModalOpen(true)}
          className="flex items-center gap-2"
        >
          <Plus className="w-4 h-4" />
          创建充值订单
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>搜索筛选</CardTitle>
        </CardHeader>
        <CardContent>
          <RechargeSearch onSearch={handleSearch} values={searchParams || {}} />
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>充值订单列表</CardTitle>
        </CardHeader>
        <CardContent>
          <DataTable
            columns={columns}
            data={isLoading ? undefined : data?.data || []}
            rowCount={data?.total || 0}
            pagination={pagination}
            setPagination={setPagination}
          />
        </CardContent>
      </Card>

      <CreateOrderModal
        open={createOrderModalOpen}
        onClose={() => setCreateOrderModalOpen(false)}
      />

      <RechargeDetailDrawer
        open={detailDrawerOpen}
        onClose={handleCloseDetailDrawer}
        order={selectedOrder}
      />

      <CorporateTransferModal
        open={corporateTransferModalOpen}
        onClose={handleCloseCorporateTransferModal}
        order={selectedOrder}
      />
    </div>
  );
}
