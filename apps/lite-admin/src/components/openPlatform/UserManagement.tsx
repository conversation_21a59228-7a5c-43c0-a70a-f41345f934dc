import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { PaginationState } from '@tanstack/react-table';
import { Card, CardContent, CardHeader, CardTitle } from '@mono/ui/card';
import { DataTable } from '@/components/dataTable';
import { UserSearch } from './UserSearch';
import { SetRemarkModal } from './SetRemarkModal';
import { getUserColumns } from './UserColumns';
import { getOpenPlatformUsers } from '@/api/openPlatform';
import { OpenPlatformUser, OpenPlatformUserParams } from '@/types/openPlatform';
import { useNavigate } from '@tanstack/react-router';

export function UserManagement() {
  const navigate = useNavigate();
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 20,
  });

  const [searchParams, setSearchParams] = useState<OpenPlatformUserParams>({});
  const [selectedUser, setSelectedUser] = useState<OpenPlatformUser | null>(
    null
  );
  const [setPriceModalOpen, setSetRemarkModalOpen] = useState(false);

  // 获取用户列表
  const { data, isLoading, error } = useQuery({
    queryKey: ['openPlatformUsers', pagination, searchParams],
    queryFn: () =>
      getOpenPlatformUsers({
        ...searchParams,
        page: pagination.pageIndex + 1,
        size: pagination.pageSize,
      }),
  });

  // 处理搜索
  const handleSearch = (params: OpenPlatformUserParams) => {
    setSearchParams(params);
    setPagination((prev) => ({ ...prev, pageIndex: 0 }));
  };

  // 设置备注
  const handleSetRemark = (user: OpenPlatformUser) => {
    setSelectedUser(user);
    setSetRemarkModalOpen(true);
  };

  // 查看用户应用
  const handleViewApps = (user: OpenPlatformUser) => {
    console.log('点击查看应用，用户ID:', user.id);
    navigate({
      to: '/open-platform/users/$userId/apps',
      params: { userId: user.id },
    });
  };

  // 关闭设置备注弹窗
  const handleCloseRemarkModal = () => {
    setSetRemarkModalOpen(false);
    setSelectedUser(null);
  };

  const columns = getUserColumns({
    onSetRemark: handleSetRemark,
    onViewApps: handleViewApps,
  });

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <p className="text-red-500 mb-2">加载失败</p>
          <p className="text-sm text-muted-foreground">
            {error instanceof Error ? error.message : '未知错误'}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold tracking-tight">用户管理</h1>
        <p className="text-muted-foreground">管理开放平台用户信息和价格设置</p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>搜索筛选</CardTitle>
        </CardHeader>
        <CardContent>
          <UserSearch onSearch={handleSearch} values={searchParams} />
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>用户列表</CardTitle>
        </CardHeader>
        <CardContent>
          <DataTable
            columns={columns}
            data={isLoading ? undefined : data?.data || []}
            rowCount={data?.total || 0}
            pagination={pagination}
            setPagination={setPagination}
          />
        </CardContent>
      </Card>

      <SetRemarkModal
        open={setPriceModalOpen}
        onClose={handleCloseRemarkModal}
        user={selectedUser}
      />
    </div>
  );
}
