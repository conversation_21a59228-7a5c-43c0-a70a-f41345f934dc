import { ColumnDef } from '@tanstack/react-table';
import { Button } from '@mono/ui/button';
import { Badge } from '@mono/ui/badge';
import {
  ExtendedOpenPlatformOrder,
  orderTypeMap,
  orderStatusMap,
  orderStatusStyleMap,
} from '@/types/openPlatform';
import { format } from 'date-fns';
import { Eye } from 'lucide-react';

interface OrderColumnsProps {
  onViewDetail: (order: ExtendedOpenPlatformOrder) => void;
}

export function getOrderColumns({
  onViewDetail,
}: OrderColumnsProps): ColumnDef<ExtendedOpenPlatformOrder>[] {
  return [
    {
      accessorKey: 'orderNo',
      header: '订单号',
      cell: ({ row }) => (
        <div className="font-mono text-sm">{row.getValue('orderNo')}</div>
      ),
    },
    {
      accessorKey: 'teamInfo',
      header: '团队信息',
      cell: ({ row }) => {
        const { teamName, code } = row.original;
        return (
          <div className="text-sm font-medium">
            团队名称: {teamName}
            <br />
            团队code: {code}
          </div>
        );
      },
    },
    {
      accessorKey: 'orderType',
      header: '订单类型',
      cell: ({ row }) => {
        const type = row.getValue('orderType') as keyof typeof orderTypeMap;
        return <Badge variant="outline">{orderTypeMap[type]}</Badge>;
      },
    },
    {
      accessorKey: 'payableAmount',
      header: '金额',
      cell: ({ row }) => {
        const amount = row.getValue('payableAmount') as number;
        return <div className="text-sm font-medium">¥{amount.toFixed(2)}</div>;
      },
    },
    {
      accessorKey: 'orderStatus',
      header: '状态',
      cell: ({ row }) => {
        const orderStatus = row.getValue(
          'orderStatus'
        ) as keyof typeof orderStatusMap;
        return (
          <Badge className={orderStatusStyleMap[orderStatus]}>
            {orderStatusMap[orderStatus]}
          </Badge>
        );
      },
    },
    {
      accessorKey: 'createdAt',
      header: '创建时间',
      cell: ({ row }) => {
        const date = row.getValue('createdAt') as string;
        return (
          <div className="text-sm">
            {date ? format(new Date(date), 'yyyy-MM-dd HH:mm:ss') : '-'}
          </div>
        );
      },
    },
    {
      accessorKey: 'payTime',
      header: '支付时间',
      cell: ({ row }) => {
        const date = row.getValue('payTime') as string;
        return (
          <div className="text-sm">
            {date ? format(new Date(date), 'yyyy-MM-dd HH:mm:ss') : '-'}
          </div>
        );
      },
    },
    {
      accessorKey: 'payAmount',
      header: '消耗蚁币',
      cell: ({ row }) => {
        const coins = row.getValue('payAmount') as number;
        return (
          <div className="text-sm font-medium text-orange-600">
            {coins.toLocaleString()}
          </div>
        );
      },
    },
    {
      id: 'actions',
      header: '操作',
      cell: ({ row }) => {
        const order = row.original;

        return (
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => onViewDetail(order)}
              className="flex items-center gap-1"
            >
              <Eye className="w-4 h-4" />
              详情
            </Button>
          </div>
        );
      },
    },
  ];
}
