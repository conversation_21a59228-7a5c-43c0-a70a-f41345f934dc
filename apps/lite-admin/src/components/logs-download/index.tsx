import { useMemo, useState } from 'react';
import { ColumnDef, PaginationState } from '@tanstack/react-table';
import { useMutation, useQuery } from '@tanstack/react-query';
import { DataTable } from '@/components/dataTable.tsx';
import { getLogs, logsDetail } from '@/api/logs.ts';
import { logsParams, logsResponse } from '@/types/logs.ts';
import { format } from 'date-fns';
import { TableSearch } from '@/components/logs-download/TableSearch.tsx';

export function LogsDownload() {
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  });

  const columns = useMemo<ColumnDef<logsResponse>[]>(() => {
    return [
      {
        header: '用户',
        accessorKey: 'nickName',
        cell: ({ row }) => {
          const { nickName, avatar, phone } = row.original;
          return (
            <div className="flex gap-2">
              <img src={avatar} className="w-10 h-10 rounded-full" alt="" />
              <div className="">
                <div className={'font-semibold'}>{nickName}</div>
                <div>{phone}</div>
              </div>
            </div>
          );
        },
      },
      {
        header: '客户端版本',
        accessorKey: 'version',
        cell: ({ row }) => {
          const { version } = row.original;
          return <div className="flex flex-col gap-1 w-56">{version}</div>;
        },
      },
      {
        header: '设备版本',
        accessorKey: 'deviceId',
        cell: ({ row }) => {
          const { deviceId } = row.original;
          return <div className="flex flex-col gap-1 w-56">{deviceId}</div>;
        },
      },
      {
        header: '创建时间',
        accessorKey: 'createdAt',
        cell: ({ row }) => {
          const { createdAt } = row.original;
          return (
            <div className="flex flex-col gap-1 w-56">
              {format(createdAt, 'yyyy-MM-dd HH:mm:ss')}
            </div>
          );
        },
      },
      {
        header: '操作',
        accessorKey: 'action',
        cell: ({ row }) => {
          const { downloadLink } = row.original;
          return (
            <>
              <div className="flex gap-2">
                <div
                  onClick={() => downloadAsZip(downloadLink, 'logs.zip')}
                  className="text-blue-500 cursor-pointer"
                >
                  下载文件
                </div>
                <a
                  className={'text-red-500 cursor-pointer'}
                  onClick={() => deleteMutation.mutate(row.original.id)}
                >
                  删除
                </a>
              </div>
            </>
          );
        },
      },
    ];
  }, []);

  const [filter, setFilter] = useState<logsParams>({
    createStartTime: undefined,
    createEndTime: undefined,
  });

  const query = useQuery({
    queryKey: ['getLogs', pagination, filter],
    queryFn: () =>
      getLogs({
        page: pagination.pageIndex + 1,
        size: pagination.pageSize,
        ...filter,
      }),
  });

  const downloadAsZip = async (url: string, fileName: string) => {
    const response = await fetch(url);
    const blob = await response.blob();

    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = fileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(link.href);
  };

  const deleteMutation = useMutation({
    mutationFn: (id: string) => logsDetail(id),
    onSuccess: () => {
      query.refetch();
    },
  });

  return (
    <>
      <div>
        <div className={'mb-4'}>
          <TableSearch
            values={filter}
            onSearch={(e) => {
              setFilter(e);
              setPagination({ ...pagination, pageIndex: 0 });
            }}
          />
        </div>
        <DataTable
          columns={columns}
          data={query.data?.data ?? []}
          rowCount={0}
          pagination={pagination}
          setPagination={setPagination}
        />
      </div>
    </>
  );
}
