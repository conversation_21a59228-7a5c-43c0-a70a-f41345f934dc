import { useForm } from 'react-hook-form';
import { Button } from '@mono/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from '@mono/ui/form';
import { Input } from '@mono/ui/input';
import { CalendarRange } from '@mono/ui/common/CalendarRange';
import { omitBy } from 'lodash';
import { logsParams } from '@/types/logs.ts';

type SearchForm = Omit<logsParams, 'createStartTime' | 'createEndTime'> & {
  createDateRange?: {
    from: Date;
    to: Date;
  };
};

interface TableSearchProps {
  values: logsParams;
  onSearch: (values: logsParams) => void;
}

export function TableSearch({ values, onSearch }: TableSearchProps) {
  const form = useForm<SearchForm>({
    defaultValues: {
      phone: values?.phone?.trim() ?? '',
      nickName: values?.nickName?.trim() ?? '',
    },
  });

  function onSubmit(values: SearchForm) {
    // Do something with the form values.
    // ✅ This will be type-safe and validated.
    console.log(values);
    const params: logsParams = {
      phone: values.phone?.trim(),
      nickName: values.nickName?.trim(),
      createStartTime: values?.createDateRange?.from?.getTime(),
      createEndTime: values.createDateRange?.to?.getTime(),
    };
    const paramsOmitEmpty = omitBy(params, (value) => !value);
    onSearch(paramsOmitEmpty);
  }

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="flex flex-wrap gap-x-4 gap-y-2 p-0.5"
      >
        <FormField
          control={form.control}
          name="nickName"
          render={({ field }) => (
            <FormItem className="flex items-center gap-1 space-y-0">
              <FormLabel>用户名</FormLabel>
              <FormControl>
                <Input className="w-40" placeholder="用户名" {...field} />
              </FormControl>
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="phone"
          render={({ field }) => (
            <FormItem className="flex items-center gap-1 space-y-0">
              <FormLabel>手机号</FormLabel>
              <FormControl>
                <Input className="w-40" placeholder="搜索手机号" {...field} />
              </FormControl>
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="createDateRange"
          render={({ field }) => (
            <FormItem className="flex items-center gap-1 space-y-0">
              <FormLabel className="">注册时间</FormLabel>
              <CalendarRange value={field.value} onChange={field.onChange} />
            </FormItem>
          )}
        />

        <Button type="submit">搜索</Button>
      </form>
    </Form>
  );
}
