import { useQuery } from '@tanstack/react-query';
import { DataTable } from '../dataTable';
import { getTeamRate } from '@/api/analysis';
import { PaginationState } from '@tanstack/react-table';
import { useMemo, useState } from 'react';
import { getColumns } from './columns';
import { TableSearch } from './TableSearch';
import { AnalysisReq } from '@/types/analysis';
import { Card } from '@mono/ui/card';
import { Tabs, TabsList, TabsTrigger } from '@mono/ui/tabs';
import { getCustomerList } from '@/api/admin';
import { ScrollArea, ScrollBar } from '@mono/ui/scroll-area';
export const PerformanceAnalysisPage = () => {
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  });
  const [filter, setFilter] = useState<AnalysisReq>();
  const [tab, setTab] = useState<string>('all');
  const query = useQuery({
    queryKey: ['getTeamRate', pagination, filter],
    queryFn: () =>
      getTeamRate({
        page: pagination.pageIndex + 1,
        size: pagination.pageSize,
        ...filter,
      }),
  });
  const columns = useMemo(() => {
    return getColumns(filter?.customerId);
  }, [filter?.customerId]);

  const channelsQuery = useQuery({
    queryFn: () => getCustomerList(),
    queryKey: ['getChannels'],
    // enabled: !!searchText,
  });

  const total = useMemo(() => {
    if (query.data) {
      return [
        {
          name: '注册团队数',
          count: query.data.registerTeamTotal,
        },
        {
          name: '过期团队数',
          count: query.data.expiredTeamTotal,
        },
        {
          name: '付费团队数',
          count: query.data.paidTeamTotal,
        },
        {
          name: '续费团队数',
          count: query.data.renewTeamTotal,
        },
        {
          name: '转化率',
          count: query.data.conversionRateTotal,
          rate: true,
        },
        {
          name: '续费率',
          count: query.data.renewRateTotal,
          rate: true,
        },
      ];
    }
  }, [query.data]);

  const setCustomer = (value: string) => {
    setPagination({ ...pagination, pageIndex: 0 });
    setFilter({ ...filter, customerId: value });
  };
  return (
    <div className="flex flex-col gap-2 overflow-hidden">
      <div className="flex items-center gap-2">
        <Tabs
          value={tab}
          onValueChange={(value) => {
            if (value === 'all') {
              setFilter({ ...filter, customerId: undefined });
            } else {
              setCustomer(channelsQuery.data?.[0]?.id || '');
            }
            setTab(value);
          }}
        >
          <TabsList>
            <TabsTrigger value="all">全部业绩</TabsTrigger>
            <TabsTrigger value="customer">客服业绩</TabsTrigger>
          </TabsList>
        </Tabs>
        <TableSearch
          values={filter}
          onSearch={(value) => {
            setPagination({ ...pagination, pageIndex: 0 });
            setFilter({
              customerId: filter?.customerId,
              ...value,
            });
          }}
        />
      </div>
      {!!filter?.customerId && (
        <Tabs value={filter.customerId} onValueChange={setCustomer}>
          <ScrollArea className="overflow-hidden w-full whitespace-nowrap">
            <TabsList className="gap-2 justify-start inline-flex">
              {channelsQuery.data?.map((item) => (
                <TabsTrigger value={item.id}>{item.name}</TabsTrigger>
              ))}
            </TabsList>
            <ScrollBar orientation="horizontal" />
          </ScrollArea>
        </Tabs>
      )}
      <Card className="grid h-20 grid-cols-6 my-1 grid-rows-1 gap-4">
        {total?.map((item) => (
          <div
            key={item.name}
            className="flex flex-col items-center justify-center"
          >
            <span className="text-2xl font-bold">
              {item.count}
              {item.rate && '%'}
            </span>
            <span className="text-sm">{item.name}</span>
          </div>
        ))}
      </Card>
      <DataTable
        columns={columns}
        data={query.data?.data}
        rowCount={query.data?.totalSize}
        pagination={pagination}
        setPagination={setPagination}
      />
    </div>
  );
};
