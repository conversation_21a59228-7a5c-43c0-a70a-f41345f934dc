('use client');

import { Area, AreaChart, CartesianGrid, XAxis } from 'recharts';

import { Card, CardContent, CardHeader, CardTitle } from '@mono/ui/card';
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from '@mono/ui/chart';
import { formatMonthDay } from '@mono/utils/day';

const chartConfig = {
  publishCount: {
    label: '作品发布数',
    color: 'hsl(var(--chart-1))',
  },
  teamPublishCount: {
    label: '活跃发布团队数',
    color: 'hsl(var(--chart-1))',
  },
  registerCount: {
    label: '新增用户数',
    color: 'hsl(var(--chart-1))',
  },
  appUserPublishCount: {
    label: 'APP发布用户数',
    color: 'hsl(var(--chart-1))',
  },
} satisfies ChartConfig;

export function AreaChartComponent({
  title,
  chartData,
}: {
  title: string;
  chartData: unknown[];
}) {
  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>{title}</CardTitle>
      </CardHeader>
      <CardContent>
        <ChartContainer config={chartConfig}>
          <AreaChart
            accessibilityLayer
            data={chartData}
            margin={{
              top: 10,
              left: 10,
              right: 12,
              bottom: 20,
            }}
          >
            <CartesianGrid vertical={false} />
            <XAxis
              dataKey="date"
              tickLine={true}
              axisLine={false}
              angle={-45}
              tickMargin={24}
              tickFormatter={(value) => formatMonthDay(value)}
            />
            {/* <YAxis
              dataKey="value"
              tickLine={false}
              axisLine={false}
              tickMargin={8}
            /> */}
            <ChartTooltip
              cursor={false}
              content={<ChartTooltipContent indicator="line" />}
            />
            <Area
              dataKey="value"
              type="monotone"
              fill="var(--color-publishCount)"
              fillOpacity={0.4}
              stroke="var(--color-publishCount)"
              dot={{
                fill: 'var(--color-publishCount)',
              }}
              activeDot={{
                r: 6,
              }}
            />
          </AreaChart>
        </ChartContainer>
      </CardContent>
    </Card>
  );
}
