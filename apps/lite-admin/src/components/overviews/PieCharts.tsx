import ReactECharts from 'echarts-for-react';
import React from 'react';

export const PieCharts = ({
  chartData,
  chartsClass,
}: {
  chartData: { name: string; value: number }[];
  chartsClass?: string;
}) => {
  const option = React.useMemo(
    () => ({
      tooltip: {
        trigger: 'item',
        // formatter: '{b}: {c} ({d}%)',
      },
      series: [
        {
          type: 'pie',
          radius: '60%',
          data: chartData,
        },
      ],
    }),
    [chartData]
  );

  return (
    <>
      <ReactECharts option={option} className={chartsClass} />
    </>
  );
};
