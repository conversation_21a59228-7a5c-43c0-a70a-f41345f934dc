import { getAccountScale } from '@/api/data';
import { Card, CardContent, CardHeader, CardTitle } from '@mono/ui/card';
import { ChartConfig, ChartContainer } from '@mono/ui/chart';
import { useQuery } from '@tanstack/react-query';
import React from 'react';
import ReactECharts from 'echarts-for-react';

const chartConfig = {
  visitors: {
    label: '数量',
    color: 'hsl(var(--chart-1))',
  },
} satisfies ChartConfig;

export function AccountScale() {
  const { data } = useQuery({
    queryKey: ['getAccountScale'],
    queryFn: getAccountScale,
  });

  const chartData = React.useMemo(() => {
    if (data) {
      return data
        .map(({ platformName: browser, count: visitors }) => ({
          browser,
          visitors,
        }))
        .sort((a, b) => b.visitors - a.visitors);
    }
    return [];
  }, [data]);
  const totalVisitors = React.useMemo(() => {
    return chartData.reduce((acc, curr) => acc + curr.visitors, 0);
  }, [chartData]);

  const option = React.useMemo(
    () => ({
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
      },
      grid: {
        left: '0%',
        right: '0%',
        bottom: '3%',
        containLabel: true,
      },
      xAxis: [
        {
          type: 'category',
          data: chartData.map(({ browser }) => browser),
          axisTick: {
            alignWithLabel: true,
          },
        },
      ],
      yAxis: [
        {
          type: 'value',
        },
      ],
      series: [
        {
          type: 'bar',
          barWidth: '60%',
          data: chartData.map(({ visitors }) => visitors),
          label: {
            show: true,
            position: 'top',
            color: '#B0BEC5',
            fontWeight: 'bold',
          },
        },
      ],
    }),
    [chartData]
  );

  return (
    <Card className="w-full h-full flex flex-col">
      <CardHeader className="">
        <CardTitle className="text-xl">
          平台账号(总数:{totalVisitors})
        </CardTitle>
      </CardHeader>
      <CardContent className="flex-1 overflow-hidden">
        <ChartContainer config={chartConfig} className="w-full h-full">
          <ReactECharts option={option} className="!w-full !h-full" />
        </ChartContainer>
      </CardContent>
    </Card>
  );
}
