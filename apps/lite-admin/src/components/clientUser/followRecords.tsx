import { getFollowRecords } from '@/api/user';
import { Card, CardDescription, CardHeader, CardTitle } from '@mono/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@mono/ui/dialog';
import { formatDate } from '@mono/utils/day';
import { VisuallyHidden } from '@radix-ui/react-visually-hidden';
import { useQuery } from '@tanstack/react-query';
import { LoadingContainer } from '../loading';

export const FollowRecords = ({
  open,
  onOpenChange,
  userId,
}: {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  userId: string;
}) => {
  const query = useQuery({
    queryKey: ['interest', userId],
    queryFn: () => getFollowRecords(userId),
    enabled: open,
  });

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-h-svh overflow-hidden flex flex-col gap-5">
        <DialogHeader>
          <DialogTitle>跟进记录</DialogTitle>
          <VisuallyHidden>
            <DialogDescription />
          </VisuallyHidden>
        </DialogHeader>
        <div className="flex-1 overflow-x-hidden overflow-y-auto">
          <div className="flex flex-col gap-3">
            {query.isLoading && <LoadingContainer />}
            {query.isError && <div>加载失败</div>}
            {query.data &&
              (query.data.length === 0 ?
                <div>无跟进记录</div>
              : query.data.map((item) => (
                  <div key={item.id}>
                    <Card>
                      <CardHeader>
                        <CardTitle className="flex justify-between">
                          <span>客服：{item.customerName} </span>
                          <span className="text-xs text-muted-foreground">
                            {formatDate(item.createdAt)}
                          </span>
                        </CardTitle>
                        <CardDescription>{item.content}</CardDescription>
                      </CardHeader>
                    </Card>
                  </div>
                )))}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
