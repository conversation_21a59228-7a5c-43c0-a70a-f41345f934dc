import { DataTable } from '@/components/dataTable';
import { getTableColumns } from './columns';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { PaginationState } from '@tanstack/react-table';
import { useCallback, useMemo, useRef, useState } from 'react';
import { addFollowRecord, changeCustomer, memberList } from '@/api/user';
import { TableSearch } from './TableSearch';
import { MemberReq } from '@/types/user';
import { useDialog } from '@mono/ui/common/DialogProvider';
import { SelectService } from '../SelectService';
import { FollowRecords } from './followRecords';
import { useSearch } from '@tanstack/react-router';
import { Input } from '@mono/ui/input';
import { DeviceRecords } from '@/components/clientUser/deviceRecords.tsx';

export function ClientUserPage() {
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  });
  const { channelCode } = useSearch({ strict: false });
  const [filter, setFilter] = useState<MemberReq>({
    channelCode,
  });
  const { open } = useDialog();
  const [openFollowRecord, setOpenFollowRecord] = useState(false);
  const [openDevice, setOpenDevice] = useState(false);
  const userId = useRef<string>();

  const switchCustomer = useRef<string>();
  const query = useQuery({
    queryKey: ['userList', pagination, filter],
    queryFn: () =>
      memberList({
        page: pagination.pageIndex + 1,
        size: pagination.pageSize,
        ...filter,
      }),
  });

  const changeCustomerMutation = useMutation({
    mutationFn: changeCustomer,
    onSuccess: () => {
      query.refetch();
    },
  });

  const queryclient = useQueryClient();
  const dialogHandler = useDialog();

  const mutation = useMutation({
    mutationFn: addFollowRecord,
    mutationKey: ['addFollowRecord'],
    onSuccess: () => {
      query.refetch();
      queryclient.refetchQueries({ queryKey: ['userList'] });
    },
  });

  const contentRef = useRef<string>();

  const onHandleAdd = useCallback(
    (followUserId: string) => {
      dialogHandler.open({
        title: '添加跟进记录',
        description: '请输入跟进内容',
        children: (
          <Input
            placeholder="请输入跟进内容"
            onChange={(e) => (contentRef.current = e.target.value)}
          />
        ),
        onSubmit: () => {
          mutation.mutate({
            followUserId: followUserId,
            content: contentRef.current!,
          });
        },
      });
    },
    [dialogHandler, mutation]
  );

  const columns = useMemo(() => {
    return getTableColumns(
      (id) => {
        open({
          title: '更换客服',
          description: '请选择新的客服',
          children: (
            <SelectService
              defaultValue={id}
              onChange={(value) => {
                switchCustomer.current = value;
              }}
            />
          ),
          onSubmit: () => {
            if (switchCustomer.current) {
              console.log('switchCustomer', switchCustomer.current);
              changeCustomerMutation.mutate({
                id,
                customerId: switchCustomer.current,
              });
            }
          },
        });
      },
      (id) => {
        userId.current = id;
        setOpenFollowRecord(true);
      },
      onHandleAdd,
      (id) => {
        userId.current = id;
        setOpenDevice(true);
      }
    );
  }, [changeCustomerMutation, open, onHandleAdd]);

  return (
    <div className="flex flex-col gap-2 overflow-hidden">
      <TableSearch
        values={filter}
        onSearch={(value) => {
          setPagination({ ...pagination, pageIndex: 0 });
          setFilter(value);
        }}
      />

      <DataTable
        columns={columns}
        data={query.data?.data}
        rowCount={query.data?.totalSize}
        pagination={pagination}
        setPagination={setPagination}
      />

      <FollowRecords
        open={openFollowRecord}
        onOpenChange={setOpenFollowRecord}
        userId={userId.current ?? ''}
      />
      {openDevice && (
        <DeviceRecords
          open={openDevice}
          onOpenChange={setOpenDevice}
          Id={userId.current ?? ''}
        />
      )}
    </div>
  );
}
