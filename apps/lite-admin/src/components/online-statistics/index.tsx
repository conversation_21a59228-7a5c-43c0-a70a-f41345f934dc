import { getOnlineScale } from '@/api/data';
import { ScrollArea } from '@mono/ui/scroll-area';
import { useQuery } from '@tanstack/react-query';
import { LoadingContainer } from '../loading';
import { AccountOnlineScale } from './accountOnline';

export const OnlineStatisticsPage = () => {
  const { data, isLoading } = useQuery({
    queryKey: ['getOnlineScale'],
    queryFn: getOnlineScale,
  });
  if (isLoading) return <LoadingContainer />;
  return (
    <ScrollArea className="h-full">
      <div className="grid grid-cols-1 gap-4 lg:grid-cols-2 2xl:grid-cols-3 3xl:grid-cols-4">
        {data?.map((x) => (
          <AccountOnlineScale data={x.stats} platformName={x.platformName} />
        ))}
      </div>
    </ScrollArea>
  );
};
