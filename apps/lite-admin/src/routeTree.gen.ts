/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

// Import Routes

import { Route as rootRoute } from './routes/__root';
import { Route as LoginImport } from './routes/login';
import { Route as HomeImport } from './routes/_home';
import { Route as IndexImport } from './routes/index';
import { Route as HomeVersionImport } from './routes/_home/version';
import { Route as HomeUpdateScriptImport } from './routes/_home/updateScript';
import { Route as HomeTeamImport } from './routes/_home/team';
import { Route as HomeSystemNotifyImport } from './routes/_home/systemNotify';
import { Route as HomePlatformAccountImport } from './routes/_home/platformAccount';
import { Route as HomePlatformStatisticsImport } from './routes/_home/platform-statistics';
import { Route as HomePerformanceAnalysisImport } from './routes/_home/performance-analysis';
import { Route as HomeOrderImport } from './routes/_home/order';
import { Route as HomeOpenPlatformImport } from './routes/_home/open-platform';
import { Route as HomeOnlineStatisticsImport } from './routes/_home/online-statistics';
import { Route as HomeLogsDownloadImport } from './routes/_home/logs-download';
import { Route as HomeDataImport } from './routes/_home/data';
import { Route as HomeClientUserImport } from './routes/_home/clientUser';
import { Route as HomeChannelImport } from './routes/_home/channel';
import { Route as HomeAdminUserImport } from './routes/_home/adminUser';
import { Route as HomeAdImport } from './routes/_home/ad';
import { Route as HomeOpenPlatformUsersImport } from './routes/_home/open-platform.users';
import { Route as HomeOpenPlatformRechargeImport } from './routes/_home/open-platform.recharge';
import { Route as HomeOpenPlatformOrdersImport } from './routes/_home/open-platform.orders';
import { Route as HomeOpenPlatformUsersIndexImport } from './routes/_home/open-platform.users.index';
import { Route as HomeOpenPlatformUsersUserIdAppsImport } from './routes/_home/open-platform.users.$userId.apps';

// Create/Update Routes

const LoginRoute = LoginImport.update({
  id: '/login',
  path: '/login',
  getParentRoute: () => rootRoute,
} as any);

const HomeRoute = HomeImport.update({
  id: '/_home',
  getParentRoute: () => rootRoute,
} as any);

const IndexRoute = IndexImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRoute,
} as any);

const HomeVersionRoute = HomeVersionImport.update({
  id: '/version',
  path: '/version',
  getParentRoute: () => HomeRoute,
} as any);

const HomeUpdateScriptRoute = HomeUpdateScriptImport.update({
  id: '/updateScript',
  path: '/updateScript',
  getParentRoute: () => HomeRoute,
} as any);

const HomeTeamRoute = HomeTeamImport.update({
  id: '/team',
  path: '/team',
  getParentRoute: () => HomeRoute,
} as any);

const HomeSystemNotifyRoute = HomeSystemNotifyImport.update({
  id: '/systemNotify',
  path: '/systemNotify',
  getParentRoute: () => HomeRoute,
} as any);

const HomePlatformAccountRoute = HomePlatformAccountImport.update({
  id: '/platformAccount',
  path: '/platformAccount',
  getParentRoute: () => HomeRoute,
} as any);

const HomePlatformStatisticsRoute = HomePlatformStatisticsImport.update({
  id: '/platform-statistics',
  path: '/platform-statistics',
  getParentRoute: () => HomeRoute,
} as any);

const HomePerformanceAnalysisRoute = HomePerformanceAnalysisImport.update({
  id: '/performance-analysis',
  path: '/performance-analysis',
  getParentRoute: () => HomeRoute,
} as any);

const HomeOrderRoute = HomeOrderImport.update({
  id: '/order',
  path: '/order',
  getParentRoute: () => HomeRoute,
} as any);

const HomeOpenPlatformRoute = HomeOpenPlatformImport.update({
  id: '/open-platform',
  path: '/open-platform',
  getParentRoute: () => HomeRoute,
} as any);

const HomeOnlineStatisticsRoute = HomeOnlineStatisticsImport.update({
  id: '/online-statistics',
  path: '/online-statistics',
  getParentRoute: () => HomeRoute,
} as any);

const HomeLogsDownloadRoute = HomeLogsDownloadImport.update({
  id: '/logs-download',
  path: '/logs-download',
  getParentRoute: () => HomeRoute,
} as any);

const HomeDataRoute = HomeDataImport.update({
  id: '/data',
  path: '/data',
  getParentRoute: () => HomeRoute,
} as any);

const HomeClientUserRoute = HomeClientUserImport.update({
  id: '/clientUser',
  path: '/clientUser',
  getParentRoute: () => HomeRoute,
} as any);

const HomeChannelRoute = HomeChannelImport.update({
  id: '/channel',
  path: '/channel',
  getParentRoute: () => HomeRoute,
} as any);

const HomeAdminUserRoute = HomeAdminUserImport.update({
  id: '/adminUser',
  path: '/adminUser',
  getParentRoute: () => HomeRoute,
} as any);

const HomeAdRoute = HomeAdImport.update({
  id: '/ad',
  path: '/ad',
  getParentRoute: () => HomeRoute,
} as any);

const HomeOpenPlatformUsersRoute = HomeOpenPlatformUsersImport.update({
  id: '/users',
  path: '/users',
  getParentRoute: () => HomeOpenPlatformRoute,
} as any);

const HomeOpenPlatformRechargeRoute = HomeOpenPlatformRechargeImport.update({
  id: '/recharge',
  path: '/recharge',
  getParentRoute: () => HomeOpenPlatformRoute,
} as any);

const HomeOpenPlatformOrdersRoute = HomeOpenPlatformOrdersImport.update({
  id: '/orders',
  path: '/orders',
  getParentRoute: () => HomeOpenPlatformRoute,
} as any);

const HomeOpenPlatformUsersIndexRoute = HomeOpenPlatformUsersIndexImport.update(
  {
    id: '/',
    path: '/',
    getParentRoute: () => HomeOpenPlatformUsersRoute,
  } as any
);

const HomeOpenPlatformUsersUserIdAppsRoute =
  HomeOpenPlatformUsersUserIdAppsImport.update({
    id: '/$userId/apps',
    path: '/$userId/apps',
    getParentRoute: () => HomeOpenPlatformUsersRoute,
  } as any);

// Populate the FileRoutesByPath interface

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/': {
      id: '/';
      path: '/';
      fullPath: '/';
      preLoaderRoute: typeof IndexImport;
      parentRoute: typeof rootRoute;
    };
    '/_home': {
      id: '/_home';
      path: '';
      fullPath: '';
      preLoaderRoute: typeof HomeImport;
      parentRoute: typeof rootRoute;
    };
    '/login': {
      id: '/login';
      path: '/login';
      fullPath: '/login';
      preLoaderRoute: typeof LoginImport;
      parentRoute: typeof rootRoute;
    };
    '/_home/ad': {
      id: '/_home/ad';
      path: '/ad';
      fullPath: '/ad';
      preLoaderRoute: typeof HomeAdImport;
      parentRoute: typeof HomeImport;
    };
    '/_home/adminUser': {
      id: '/_home/adminUser';
      path: '/adminUser';
      fullPath: '/adminUser';
      preLoaderRoute: typeof HomeAdminUserImport;
      parentRoute: typeof HomeImport;
    };
    '/_home/channel': {
      id: '/_home/channel';
      path: '/channel';
      fullPath: '/channel';
      preLoaderRoute: typeof HomeChannelImport;
      parentRoute: typeof HomeImport;
    };
    '/_home/clientUser': {
      id: '/_home/clientUser';
      path: '/clientUser';
      fullPath: '/clientUser';
      preLoaderRoute: typeof HomeClientUserImport;
      parentRoute: typeof HomeImport;
    };
    '/_home/data': {
      id: '/_home/data';
      path: '/data';
      fullPath: '/data';
      preLoaderRoute: typeof HomeDataImport;
      parentRoute: typeof HomeImport;
    };
    '/_home/logs-download': {
      id: '/_home/logs-download';
      path: '/logs-download';
      fullPath: '/logs-download';
      preLoaderRoute: typeof HomeLogsDownloadImport;
      parentRoute: typeof HomeImport;
    };
    '/_home/online-statistics': {
      id: '/_home/online-statistics';
      path: '/online-statistics';
      fullPath: '/online-statistics';
      preLoaderRoute: typeof HomeOnlineStatisticsImport;
      parentRoute: typeof HomeImport;
    };
    '/_home/open-platform': {
      id: '/_home/open-platform';
      path: '/open-platform';
      fullPath: '/open-platform';
      preLoaderRoute: typeof HomeOpenPlatformImport;
      parentRoute: typeof HomeImport;
    };
    '/_home/order': {
      id: '/_home/order';
      path: '/order';
      fullPath: '/order';
      preLoaderRoute: typeof HomeOrderImport;
      parentRoute: typeof HomeImport;
    };
    '/_home/performance-analysis': {
      id: '/_home/performance-analysis';
      path: '/performance-analysis';
      fullPath: '/performance-analysis';
      preLoaderRoute: typeof HomePerformanceAnalysisImport;
      parentRoute: typeof HomeImport;
    };
    '/_home/platform-statistics': {
      id: '/_home/platform-statistics';
      path: '/platform-statistics';
      fullPath: '/platform-statistics';
      preLoaderRoute: typeof HomePlatformStatisticsImport;
      parentRoute: typeof HomeImport;
    };
    '/_home/platformAccount': {
      id: '/_home/platformAccount';
      path: '/platformAccount';
      fullPath: '/platformAccount';
      preLoaderRoute: typeof HomePlatformAccountImport;
      parentRoute: typeof HomeImport;
    };
    '/_home/systemNotify': {
      id: '/_home/systemNotify';
      path: '/systemNotify';
      fullPath: '/systemNotify';
      preLoaderRoute: typeof HomeSystemNotifyImport;
      parentRoute: typeof HomeImport;
    };
    '/_home/team': {
      id: '/_home/team';
      path: '/team';
      fullPath: '/team';
      preLoaderRoute: typeof HomeTeamImport;
      parentRoute: typeof HomeImport;
    };
    '/_home/updateScript': {
      id: '/_home/updateScript';
      path: '/updateScript';
      fullPath: '/updateScript';
      preLoaderRoute: typeof HomeUpdateScriptImport;
      parentRoute: typeof HomeImport;
    };
    '/_home/version': {
      id: '/_home/version';
      path: '/version';
      fullPath: '/version';
      preLoaderRoute: typeof HomeVersionImport;
      parentRoute: typeof HomeImport;
    };
    '/_home/open-platform/orders': {
      id: '/_home/open-platform/orders';
      path: '/orders';
      fullPath: '/open-platform/orders';
      preLoaderRoute: typeof HomeOpenPlatformOrdersImport;
      parentRoute: typeof HomeOpenPlatformImport;
    };
    '/_home/open-platform/recharge': {
      id: '/_home/open-platform/recharge';
      path: '/recharge';
      fullPath: '/open-platform/recharge';
      preLoaderRoute: typeof HomeOpenPlatformRechargeImport;
      parentRoute: typeof HomeOpenPlatformImport;
    };
    '/_home/open-platform/users': {
      id: '/_home/open-platform/users';
      path: '/users';
      fullPath: '/open-platform/users';
      preLoaderRoute: typeof HomeOpenPlatformUsersImport;
      parentRoute: typeof HomeOpenPlatformImport;
    };
    '/_home/open-platform/users/': {
      id: '/_home/open-platform/users/';
      path: '/';
      fullPath: '/open-platform/users/';
      preLoaderRoute: typeof HomeOpenPlatformUsersIndexImport;
      parentRoute: typeof HomeOpenPlatformUsersImport;
    };
    '/_home/open-platform/users/$userId/apps': {
      id: '/_home/open-platform/users/$userId/apps';
      path: '/$userId/apps';
      fullPath: '/open-platform/users/$userId/apps';
      preLoaderRoute: typeof HomeOpenPlatformUsersUserIdAppsImport;
      parentRoute: typeof HomeOpenPlatformUsersImport;
    };
  }
}

// Create and export the route tree

interface HomeOpenPlatformUsersRouteChildren {
  HomeOpenPlatformUsersIndexRoute: typeof HomeOpenPlatformUsersIndexRoute;
  HomeOpenPlatformUsersUserIdAppsRoute: typeof HomeOpenPlatformUsersUserIdAppsRoute;
}

const HomeOpenPlatformUsersRouteChildren: HomeOpenPlatformUsersRouteChildren = {
  HomeOpenPlatformUsersIndexRoute: HomeOpenPlatformUsersIndexRoute,
  HomeOpenPlatformUsersUserIdAppsRoute: HomeOpenPlatformUsersUserIdAppsRoute,
};

const HomeOpenPlatformUsersRouteWithChildren =
  HomeOpenPlatformUsersRoute._addFileChildren(
    HomeOpenPlatformUsersRouteChildren
  );

interface HomeOpenPlatformRouteChildren {
  HomeOpenPlatformOrdersRoute: typeof HomeOpenPlatformOrdersRoute;
  HomeOpenPlatformRechargeRoute: typeof HomeOpenPlatformRechargeRoute;
  HomeOpenPlatformUsersRoute: typeof HomeOpenPlatformUsersRouteWithChildren;
}

const HomeOpenPlatformRouteChildren: HomeOpenPlatformRouteChildren = {
  HomeOpenPlatformOrdersRoute: HomeOpenPlatformOrdersRoute,
  HomeOpenPlatformRechargeRoute: HomeOpenPlatformRechargeRoute,
  HomeOpenPlatformUsersRoute: HomeOpenPlatformUsersRouteWithChildren,
};

const HomeOpenPlatformRouteWithChildren =
  HomeOpenPlatformRoute._addFileChildren(HomeOpenPlatformRouteChildren);

interface HomeRouteChildren {
  HomeAdRoute: typeof HomeAdRoute;
  HomeAdminUserRoute: typeof HomeAdminUserRoute;
  HomeChannelRoute: typeof HomeChannelRoute;
  HomeClientUserRoute: typeof HomeClientUserRoute;
  HomeDataRoute: typeof HomeDataRoute;
  HomeLogsDownloadRoute: typeof HomeLogsDownloadRoute;
  HomeOnlineStatisticsRoute: typeof HomeOnlineStatisticsRoute;
  HomeOpenPlatformRoute: typeof HomeOpenPlatformRouteWithChildren;
  HomeOrderRoute: typeof HomeOrderRoute;
  HomePerformanceAnalysisRoute: typeof HomePerformanceAnalysisRoute;
  HomePlatformStatisticsRoute: typeof HomePlatformStatisticsRoute;
  HomePlatformAccountRoute: typeof HomePlatformAccountRoute;
  HomeSystemNotifyRoute: typeof HomeSystemNotifyRoute;
  HomeTeamRoute: typeof HomeTeamRoute;
  HomeUpdateScriptRoute: typeof HomeUpdateScriptRoute;
  HomeVersionRoute: typeof HomeVersionRoute;
}

const HomeRouteChildren: HomeRouteChildren = {
  HomeAdRoute: HomeAdRoute,
  HomeAdminUserRoute: HomeAdminUserRoute,
  HomeChannelRoute: HomeChannelRoute,
  HomeClientUserRoute: HomeClientUserRoute,
  HomeDataRoute: HomeDataRoute,
  HomeLogsDownloadRoute: HomeLogsDownloadRoute,
  HomeOnlineStatisticsRoute: HomeOnlineStatisticsRoute,
  HomeOpenPlatformRoute: HomeOpenPlatformRouteWithChildren,
  HomeOrderRoute: HomeOrderRoute,
  HomePerformanceAnalysisRoute: HomePerformanceAnalysisRoute,
  HomePlatformStatisticsRoute: HomePlatformStatisticsRoute,
  HomePlatformAccountRoute: HomePlatformAccountRoute,
  HomeSystemNotifyRoute: HomeSystemNotifyRoute,
  HomeTeamRoute: HomeTeamRoute,
  HomeUpdateScriptRoute: HomeUpdateScriptRoute,
  HomeVersionRoute: HomeVersionRoute,
};

const HomeRouteWithChildren = HomeRoute._addFileChildren(HomeRouteChildren);

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute;
  '': typeof HomeRouteWithChildren;
  '/login': typeof LoginRoute;
  '/ad': typeof HomeAdRoute;
  '/adminUser': typeof HomeAdminUserRoute;
  '/channel': typeof HomeChannelRoute;
  '/clientUser': typeof HomeClientUserRoute;
  '/data': typeof HomeDataRoute;
  '/logs-download': typeof HomeLogsDownloadRoute;
  '/online-statistics': typeof HomeOnlineStatisticsRoute;
  '/open-platform': typeof HomeOpenPlatformRouteWithChildren;
  '/order': typeof HomeOrderRoute;
  '/performance-analysis': typeof HomePerformanceAnalysisRoute;
  '/platform-statistics': typeof HomePlatformStatisticsRoute;
  '/platformAccount': typeof HomePlatformAccountRoute;
  '/systemNotify': typeof HomeSystemNotifyRoute;
  '/team': typeof HomeTeamRoute;
  '/updateScript': typeof HomeUpdateScriptRoute;
  '/version': typeof HomeVersionRoute;
  '/open-platform/orders': typeof HomeOpenPlatformOrdersRoute;
  '/open-platform/recharge': typeof HomeOpenPlatformRechargeRoute;
  '/open-platform/users': typeof HomeOpenPlatformUsersRouteWithChildren;
  '/open-platform/users/': typeof HomeOpenPlatformUsersIndexRoute;
  '/open-platform/users/$userId/apps': typeof HomeOpenPlatformUsersUserIdAppsRoute;
}

export interface FileRoutesByTo {
  '/': typeof IndexRoute;
  '': typeof HomeRouteWithChildren;
  '/login': typeof LoginRoute;
  '/ad': typeof HomeAdRoute;
  '/adminUser': typeof HomeAdminUserRoute;
  '/channel': typeof HomeChannelRoute;
  '/clientUser': typeof HomeClientUserRoute;
  '/data': typeof HomeDataRoute;
  '/logs-download': typeof HomeLogsDownloadRoute;
  '/online-statistics': typeof HomeOnlineStatisticsRoute;
  '/open-platform': typeof HomeOpenPlatformRouteWithChildren;
  '/order': typeof HomeOrderRoute;
  '/performance-analysis': typeof HomePerformanceAnalysisRoute;
  '/platform-statistics': typeof HomePlatformStatisticsRoute;
  '/platformAccount': typeof HomePlatformAccountRoute;
  '/systemNotify': typeof HomeSystemNotifyRoute;
  '/team': typeof HomeTeamRoute;
  '/updateScript': typeof HomeUpdateScriptRoute;
  '/version': typeof HomeVersionRoute;
  '/open-platform/orders': typeof HomeOpenPlatformOrdersRoute;
  '/open-platform/recharge': typeof HomeOpenPlatformRechargeRoute;
  '/open-platform/users': typeof HomeOpenPlatformUsersIndexRoute;
  '/open-platform/users/$userId/apps': typeof HomeOpenPlatformUsersUserIdAppsRoute;
}

export interface FileRoutesById {
  __root__: typeof rootRoute;
  '/': typeof IndexRoute;
  '/_home': typeof HomeRouteWithChildren;
  '/login': typeof LoginRoute;
  '/_home/ad': typeof HomeAdRoute;
  '/_home/adminUser': typeof HomeAdminUserRoute;
  '/_home/channel': typeof HomeChannelRoute;
  '/_home/clientUser': typeof HomeClientUserRoute;
  '/_home/data': typeof HomeDataRoute;
  '/_home/logs-download': typeof HomeLogsDownloadRoute;
  '/_home/online-statistics': typeof HomeOnlineStatisticsRoute;
  '/_home/open-platform': typeof HomeOpenPlatformRouteWithChildren;
  '/_home/order': typeof HomeOrderRoute;
  '/_home/performance-analysis': typeof HomePerformanceAnalysisRoute;
  '/_home/platform-statistics': typeof HomePlatformStatisticsRoute;
  '/_home/platformAccount': typeof HomePlatformAccountRoute;
  '/_home/systemNotify': typeof HomeSystemNotifyRoute;
  '/_home/team': typeof HomeTeamRoute;
  '/_home/updateScript': typeof HomeUpdateScriptRoute;
  '/_home/version': typeof HomeVersionRoute;
  '/_home/open-platform/orders': typeof HomeOpenPlatformOrdersRoute;
  '/_home/open-platform/recharge': typeof HomeOpenPlatformRechargeRoute;
  '/_home/open-platform/users': typeof HomeOpenPlatformUsersRouteWithChildren;
  '/_home/open-platform/users/': typeof HomeOpenPlatformUsersIndexRoute;
  '/_home/open-platform/users/$userId/apps': typeof HomeOpenPlatformUsersUserIdAppsRoute;
}

export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath;
  fullPaths:
    | '/'
    | ''
    | '/login'
    | '/ad'
    | '/adminUser'
    | '/channel'
    | '/clientUser'
    | '/data'
    | '/logs-download'
    | '/online-statistics'
    | '/open-platform'
    | '/order'
    | '/performance-analysis'
    | '/platform-statistics'
    | '/platformAccount'
    | '/systemNotify'
    | '/team'
    | '/updateScript'
    | '/version'
    | '/open-platform/orders'
    | '/open-platform/recharge'
    | '/open-platform/users'
    | '/open-platform/users/'
    | '/open-platform/users/$userId/apps';
  fileRoutesByTo: FileRoutesByTo;
  to:
    | '/'
    | ''
    | '/login'
    | '/ad'
    | '/adminUser'
    | '/channel'
    | '/clientUser'
    | '/data'
    | '/logs-download'
    | '/online-statistics'
    | '/open-platform'
    | '/order'
    | '/performance-analysis'
    | '/platform-statistics'
    | '/platformAccount'
    | '/systemNotify'
    | '/team'
    | '/updateScript'
    | '/version'
    | '/open-platform/orders'
    | '/open-platform/recharge'
    | '/open-platform/users'
    | '/open-platform/users/$userId/apps';
  id:
    | '__root__'
    | '/'
    | '/_home'
    | '/login'
    | '/_home/ad'
    | '/_home/adminUser'
    | '/_home/channel'
    | '/_home/clientUser'
    | '/_home/data'
    | '/_home/logs-download'
    | '/_home/online-statistics'
    | '/_home/open-platform'
    | '/_home/order'
    | '/_home/performance-analysis'
    | '/_home/platform-statistics'
    | '/_home/platformAccount'
    | '/_home/systemNotify'
    | '/_home/team'
    | '/_home/updateScript'
    | '/_home/version'
    | '/_home/open-platform/orders'
    | '/_home/open-platform/recharge'
    | '/_home/open-platform/users'
    | '/_home/open-platform/users/'
    | '/_home/open-platform/users/$userId/apps';
  fileRoutesById: FileRoutesById;
}

export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute;
  HomeRoute: typeof HomeRouteWithChildren;
  LoginRoute: typeof LoginRoute;
}

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  HomeRoute: HomeRouteWithChildren,
  LoginRoute: LoginRoute,
};

export const routeTree = rootRoute
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>();

/* ROUTE_MANIFEST_START
{
  "routes": {
    "__root__": {
      "filePath": "__root.tsx",
      "children": [
        "/",
        "/_home",
        "/login"
      ]
    },
    "/": {
      "filePath": "index.tsx"
    },
    "/_home": {
      "filePath": "_home.tsx",
      "children": [
        "/_home/ad",
        "/_home/adminUser",
        "/_home/channel",
        "/_home/clientUser",
        "/_home/data",
        "/_home/logs-download",
        "/_home/online-statistics",
        "/_home/open-platform",
        "/_home/order",
        "/_home/performance-analysis",
        "/_home/platform-statistics",
        "/_home/platformAccount",
        "/_home/systemNotify",
        "/_home/team",
        "/_home/updateScript",
        "/_home/version"
      ]
    },
    "/login": {
      "filePath": "login.tsx"
    },
    "/_home/ad": {
      "filePath": "_home/ad.tsx",
      "parent": "/_home"
    },
    "/_home/adminUser": {
      "filePath": "_home/adminUser.tsx",
      "parent": "/_home"
    },
    "/_home/channel": {
      "filePath": "_home/channel.tsx",
      "parent": "/_home"
    },
    "/_home/clientUser": {
      "filePath": "_home/clientUser.tsx",
      "parent": "/_home"
    },
    "/_home/data": {
      "filePath": "_home/data.tsx",
      "parent": "/_home"
    },
    "/_home/logs-download": {
      "filePath": "_home/logs-download.tsx",
      "parent": "/_home"
    },
    "/_home/online-statistics": {
      "filePath": "_home/online-statistics.tsx",
      "parent": "/_home"
    },
    "/_home/open-platform": {
      "filePath": "_home/open-platform.tsx",
      "parent": "/_home",
      "children": [
        "/_home/open-platform/orders",
        "/_home/open-platform/recharge",
        "/_home/open-platform/users"
      ]
    },
    "/_home/order": {
      "filePath": "_home/order.tsx",
      "parent": "/_home"
    },
    "/_home/performance-analysis": {
      "filePath": "_home/performance-analysis.tsx",
      "parent": "/_home"
    },
    "/_home/platform-statistics": {
      "filePath": "_home/platform-statistics.tsx",
      "parent": "/_home"
    },
    "/_home/platformAccount": {
      "filePath": "_home/platformAccount.tsx",
      "parent": "/_home"
    },
    "/_home/systemNotify": {
      "filePath": "_home/systemNotify.tsx",
      "parent": "/_home"
    },
    "/_home/team": {
      "filePath": "_home/team.tsx",
      "parent": "/_home"
    },
    "/_home/updateScript": {
      "filePath": "_home/updateScript.tsx",
      "parent": "/_home"
    },
    "/_home/version": {
      "filePath": "_home/version.tsx",
      "parent": "/_home"
    },
    "/_home/open-platform/orders": {
      "filePath": "_home/open-platform.orders.tsx",
      "parent": "/_home/open-platform"
    },
    "/_home/open-platform/recharge": {
      "filePath": "_home/open-platform.recharge.tsx",
      "parent": "/_home/open-platform"
    },
    "/_home/open-platform/users": {
      "filePath": "_home/open-platform.users.tsx",
      "parent": "/_home/open-platform",
      "children": [
        "/_home/open-platform/users/",
        "/_home/open-platform/users/$userId/apps"
      ]
    },
    "/_home/open-platform/users/": {
      "filePath": "_home/open-platform.users.index.tsx",
      "parent": "/_home/open-platform/users"
    },
    "/_home/open-platform/users/$userId/apps": {
      "filePath": "_home/open-platform.users.$userId.apps.tsx",
      "parent": "/_home/open-platform/users"
    }
  }
}
ROUTE_MANIFEST_END */
