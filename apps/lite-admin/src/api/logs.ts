import { makeRequest } from '@/api/index.ts';
import { PageResult } from '@/types';
import { logsParams, logsResponse } from '@/types/logs.ts';

export const getLogs = (params: logsParams) => {
  return makeRequest<PageResult<logsResponse>>({
    url: `/members/devices/logs`,
    method: 'GET',
    params,
  });
};

export const logsDetail = (id: string) => {
  return makeRequest<logsResponse>({
    url: `/members/devices/logs/${id}`,
    method: 'DELETE',
  });
};
