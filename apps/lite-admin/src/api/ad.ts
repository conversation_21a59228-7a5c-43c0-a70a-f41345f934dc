import { makeRequest } from './index';
import { PageCount<PERSON><PERSON><PERSON>, PageResult } from '@/types/index';
import { AdDto, AddAdConfigInput, AdListInput } from '@/types/ad';

/**
 * 获取广告列表
 * GET /ad
 */
export const adList = (params: PageCountQuery & AdListInput) => {
  if (params) {
    if (!params.name) {
      delete params.name;
    }
    if (!params.enabled || params.enabled === 'all') {
      delete params.enabled;
    }
    if (!params.adType || params.adType === 'all') {
      delete params.adType;
    }
  }
  return makeRequest<PageResult<AdDto>>({
    url: '/ad',
    method: 'GET',
    params,
  });
};

/**
 * 新增广告
 * Post /ad/{id}
 */
export const addAdConfig = async (input: AddAdConfigInput) => {
  const addData: AddAdConfigInput = {
    name: input.name,
    sort: input.sort,
    adUrl: input.adUrl,
    enabled: input.enabled,
    isTimed: input.isTimed,
    isJumpTo: input.isJumpTo,
    adType: input.adType,
  };

  if (input.isJumpTo) {
    addData.jumpToUrl = input.jumpToUrl;
  }
  if (input.isTimed) {
    addData.expiredStartAt = input.expiredStartAt;
    addData.expiredEndAt = input.expiredEndAt;
  }
  if (input.popupType) {
    addData.popupType = input.popupType;
  }
  console.log('新增广告数据', addData);
  return makeRequest<object>({
    url: `/ad`,
    method: 'Post',
    data: addData,
  });
};

/**
 * 设置广告
 * Put /ad/{id}
 */
export const setAdConfig = async (input: AddAdConfigInput) => {
  const setData: AddAdConfigInput = {
    name: input.name,
    sort: input.sort,
    enabled: input.enabled,
    isTimed: input.isTimed,
    isJumpTo: input.isJumpTo,
    adType: input.adType,
  };

  if (input.isJumpTo) {
    setData.jumpToUrl = input.jumpToUrl;
  }
  if (input.adUrl) {
    setData.adUrl = input.adUrl;
  }
  if (input.isTimed) {
    setData.expiredStartAt = input.expiredStartAt;
    setData.expiredEndAt = input.expiredEndAt;
  }
  if (input.popupType) {
    setData.popupType = input.popupType;
  }
  return makeRequest<object>({
    url: `/ad/${input.id}`,
    method: 'Patch',
    data: setData,
  });
};

/**
 * 广告详情
 * @param id
 * @returns
 */
export const getAdDetail = async (id: string) => {
  return makeRequest<object>({
    url: `/ad/${id}`,
    method: 'Get',
  });
};

/**
 * 删除广告
 * Delete /ad/{id}
 */
export const deleteAd = (id: string) => {
  return makeRequest<object>({
    url: `/ad/${id}`,
    method: 'Delete',
  });
};

/**
 * 更新上下架
 * @param id
 * @param enabled
 * @returns
 */
export const putAdEnabled = (id: string, enabled: boolean) => {
  return makeRequest<object>({
    url: `/ad/${id}/enabled`,
    method: 'Put',
    data: {
      enabled: enabled,
    },
  });
};
