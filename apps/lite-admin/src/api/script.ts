import { PageCountQ<PERSON>y, PageResult } from '@/types';
import { createScriptInput, IScriptType } from '@/types/scriptType.ts';
import { makeRequest } from '@/api/index.ts';

export const getScriptList = (
  _params: PageCountQuery,
  type: 'rpa' | 'spider'
) => {
  const params = { ..._params, type };
  return makeRequest<PageResult<IScriptType>>({
    url: `/online-scripts`,
    method: 'GET',
    params,
  });
};

export const createScript = (params: createScriptInput) => {
  return makeRequest<IScriptType>({
    url: `/online-scripts`,
    method: 'POST',
    data: params,
  });
};
