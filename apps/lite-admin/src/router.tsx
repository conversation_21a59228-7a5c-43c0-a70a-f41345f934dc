import { ErrorComponent, createRouter } from '@tanstack/react-router';
import { Spinner } from './components/spinner';
import { routeTree } from './routeTree.gen';

export const router = createRouter({
  routeTree,
  defaultPendingComponent: () => (
    <div className="flex h-full w-full items-center justify-center">
      <Spinner />
    </div>
  ),
  defaultErrorComponent: ({ error }) => <ErrorComponent error={error} />,
  defaultPreload: 'intent',
  defaultPreloadStaleTime: 0,
});
