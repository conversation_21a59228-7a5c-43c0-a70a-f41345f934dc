import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import { UserAuthorization, UserInfo } from '@/types/user';

type UserState = {
  userInfo?: UserInfo;
  userAuthorization?: UserAuthorization;
  setUserInfo: (userInfo?: UserInfo) => void;
  setUserAuthorization: (userAuthorization?: UserAuthorization) => void;
  isSuper: boolean;
  setIsSuper: (isSuper: boolean) => void;
};

export const useUserStore = create<UserState>()(
  persist(
    immer((set) => ({
      userInfo: undefined,
      setUserInfo: (userInfo) => set({ userInfo }),
      userAuthorization: undefined,
      setUserAuthorization: (userAuthorization) => set({ userAuthorization }),
      isSuper: false,
      setIsSuper: (isSuper) => set({ isSuper }),
    })),
    {
      name: 'yixiaoer-lite-admin',
      storage: createJSONStorage(() => localStorage),
    }
  )
);
