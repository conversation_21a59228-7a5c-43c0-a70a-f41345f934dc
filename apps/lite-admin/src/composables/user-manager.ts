import { UserManager, WebStorageStateStore } from 'oidc-client-ts';

const userManager = new UserManager({
  authority: import.meta.env.VITE_OPENID_ISSUER_URL,
  client_id: import.meta.env.VITE_OPENID_CLIENT_ID,
  client_secret: import.meta.env.VITE_OPENID_CLIENT_SECRET,
  response_type: 'password',
  scope: 'offline_access YixiaoerLite.api',
  redirect_uri: import.meta.env.VITE_OPENID_REDIRECT_URL,
  userStore: new WebStorageStateStore({ store: window.localStorage }),
  loadUserInfo: true,
  automaticSilentRenew: true,
});

export default userManager;
