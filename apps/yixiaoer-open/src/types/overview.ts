export interface OverviewType {
  auto: Overview[];
  normal: Overview[];
}

/**
 * Overview
 */
export interface Overview {
  commentTotalCount: number;
  groupTotalCount: number;
  singleTotalCount: number;
  teamAvatar: string;
  teamId: number;
  teamName: string;
  totalCount: number;
  receiveCommentCount: number;
  receiveGroupCount: number;
  receiveSingleCount: number;
  receiveTotalCount: number;
  invitationCode: string;
}
