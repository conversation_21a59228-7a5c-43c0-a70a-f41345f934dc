export interface Team {
  id: string;
  name: string;
  accountCapacityLimit: number;
  accountCapacity: number;
  memberCount: number;
  networkTraffic: number;
  useNetworkTraffic: number;
  createdAt: string;
  expiredAt: string;
  code: string;
  isVip: boolean;
}

export type TeamsReq = {
  /**
   * vip开通 true已开通 false未开通
   */
  isVip?: boolean;
  /**
   * 团队名称查询｜团队编号
   */
  teamName?: string;
};

export type TeamConfigInput = {
  id: string;
  enabled: boolean;
  memberCountLimit: number;
  accountCountLimit: number;
  capacity: string;
  expiredAt: undefined | number;
};
