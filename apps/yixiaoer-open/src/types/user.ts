export interface UserSendCodeReq {
  phone: string;
  captchaVerifyParam?: string;
}

export type UserLoginReq = {
  phone: string;
  code: string;
  password?: string;
  nickname?: string;
};

export type UserLogin = {
  authorization: string;
  userInfo: UserInfo;
};

export type UserInfo = {
  id: string;
  phone: string;
  nickname: string;
  status: number;
  avatar?: string;
  username?: string;
};

/**
 * MemberResponse
 */
export interface User {
  avatar: string;
  channel: Channel;
  createTime: number;
  id: number;
  name: string;
  phone: string;
}

/**
 * Channel
 */
export interface Channel {
  id: number;
  name: string;
}

export type UserParams = {
  phone?: string;
  startTime?: number;
  endTime?: number;
  channelId?: string;
};

export interface Member {
  id: string;
  /**
   * 头像
   */
  avatar: string;

  /**
   * 注册时间
   */
  createdAt: number;

  /**
   * 昵称
   */
  nickName: string;

  /**
   * 手机号
   */
  phone: string;

  /**
   * 最后使用时间
   */
  updatedAt: number;
}

export type MemberReq = {
  /**
   * 渠道码
   */
  channelCode?: string;
  /**
   * 注册结束时间
   */
  createEndTime?: number;
  /**
   * 注册开始时间
   */
  createStartTime?: number;
  /**
   * 归属人ID
   */
  customerId?: string;
  /**
   * 最后使用结束时间
   */
  lastUseEndTime?: number;
  /**
   * 最后使用开始时间
   */
  lastUseStartTime?: number;
  /**
   * 手机号
   */
  phone?: string;
  /**
   * 应用id
   */
  applicationId?: string;
};

export type CreateUserReq = {
  /**
   * 手机号
   */
  phone: string;
  /**
   * 密码
   */
  password: string;
};
