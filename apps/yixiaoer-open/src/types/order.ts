export type PayType = 'wechat' | 'alipay' | 'corporateTransfer' | 'iosPay';

//  订单号 | 手机号 | 版本 | 账号数 | 团队人数 | 生效时间 | 到期时间 | 支付时间 | 订单金额 | 订单来源(线上/线下)
export type Order = {
  id: string;
  orderNo: string;
  orderStatus: string;
  orderType: string;
  resourceType: string;
  teamId: string;
  teamName: string;
  code: string;
  userId: string;
  totalAmount: number;
  payableAmount: number;
  payTime: number;
  payAmount: number;
  accountCapacity: number;
  trafficCount: number;
  duration: number;
  startTime: number;
  endTime: number;
  trafficExpiredAt: number;
  remark: string;
  createdAt: number;
  updatedAt: number;
};

export interface VipOrderData {
  commentDosageLimit: number;
  createTime: Date;
  expirationTime: Date;
  fansGroupManageLimit: number;
  groupDosageLimit: number;
  platformAccountNumberLimit: number;
  signDosageLimit: number;
  teamMemberNumberLimit: number;
  uploadImageLimit: number;
}

export type OrderParams = {
  orderNo?: string;
  teamId?: string;
  paymentStartTime?: number;
  paymentEndTime?: number;
  orderType?: string;
  code?: string;
};

/**
 * VipCreateDTO
 */
export type OrderCreateDTO = {
  /**
   * 权益包数量
   */
  interestCount: number;
  /**
   * 权益包id
   */
  interestId: string;
  /**
   * 月份数量
   */
  month: number;
  /**
   * 实付金额
   */
  payAmount: number;
  teamId: string;
  isPay: boolean;
  remark?: string;
  /**
   * 自定义天数
   */
  days: number;
};

export type OrderStatus = 'pending' | 'success' | 'canceled' | 'refund';

export type OrderType = 'renew' | 'create' | 'upgrade' | 'gift';

export type SaleType = 'NotBuy' | 'FirstBuy' | 'ReBuy' | 'Buy';

export interface VipData {
  interests: OrderInterestResponse[];
  vipOften: OrderInterestVipOftenDTO[];
}

export type Interest = {
  id: string;
  platformAccountCount: number;
  capacityLimit: number;
  appPublish: boolean;
  memberCount: number;
  price: number;
  vipOften: {
    mount: number;
    present: number;
  }[];
};

/**
 * OrderInterestResponse
 */
export interface OrderInterestResponse {
  id: number;
  memberCount: number;
  messageCount: number;
  platformAccountCount: number;
  price: number;
}

/**
 * OrderInterestVipOftenDTO
 */
export interface OrderInterestVipOftenDTO {
  mount: number;
  present: number;
}

export interface OrderDetail {
  orderInfo: Omit<Order, 'remainingTimeInSeconds'> & {
    teamName: string;
    creatorName: string;
    remainingDays: number;
    giftDays: number;
    days?: number;
    invitationCode?: string;
    isGiftOrder?: boolean;
  };
  vipInfo: VipInfo;
}

/**
 * OrderInfo
 */
// export interface OrderInfo {
//   dueAmount: number;
//   fromTime: number;
//   orderNo: string;
//   orderStatus: OrderStatus;
//   payAmount: number;
//   payTime: number;
//   payType: string;
//   teamName: string;
//   type: 'online' | 'system';
// }

/**
 * VipInfo
 */
export interface VipInfo {
  expirationTime: number;
  freeMonth: number;
  interestCount: number;
  messageCount: number;
  month: number;
  platformAccountCount: number;
  teamMemberCount: number;
  giftDays: number;
}

export interface OrderPriceReq {
  couponId?: number;
  /**
   * 权益包数量
   */
  interestCount?: number;
  /**
   * 权益包id
   */
  interestId: string;
  /**
   * 月份数量
   */
  month?: number;
  /**
   * 订单类型(create:开通,upgrade:升级,renew:续费)
   */
  orderType: OrderType;
  /**
   * 团队id
   */
  teamId: string;
  /**
   * 天数
   */
  days?: number;
}
