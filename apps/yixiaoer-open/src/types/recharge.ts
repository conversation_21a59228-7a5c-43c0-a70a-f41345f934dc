// 充值记录类型定义
export interface RechargeRecord {
  id: string;
  applicationId: string;
  applicationName: string;
  rechargeOrderNo: string;
  rechargeType: RechargeType;
  rechargeAmount: number;
  virtualCoinAmount: number;
  paymentAmount: number;
  rechargeStatus: RechargeStatus;
  rechargeTime: number;
  remark: string;
  operatorId: string;
  createdAt: number;
  updatedAt: number;
}

// 支付方式
export type PaymentMethod = 'wechat' | 'alipay' | 'BANK_TRANSFER' | 'other';

// 充值类型
export type RechargeType = 'GIFT' | 'BANK_TRANSFER';

// 充值状态
export type RechargeStatus = 'PENDING' | 'SUCCESS' | 'FAILED' | 'CANCELLED';

// 充值列表查询参数
export interface RechargeParams {
  applicationId?: string;
  rechargeStatus?: RechargeStatus;
  rechargeType?: RechargeType;
}

// 支付方式映射
export const paymentMethodMap: Record<PaymentMethod, string> = {
  wechat: '微信支付',
  alipay: '支付宝',
  BANK_TRANSFER: '银行转账',
  other: '其他',
};

// 充值类型映射
export const rechargeTypeMap: Record<RechargeType, string> = {
  GIFT: '赠送',
  BANK_TRANSFER: '银行转账',
};

// 充值状态映射
export const rechargeStatusMap: Record<RechargeStatus, string> = {
  PENDING: '待支付',
  SUCCESS: '充值成功',
  FAILED: '充值失败',
  CANCELLED: '处理中',
};

// 充值状态样式映射
export const rechargeStatusStyleMap: Record<RechargeStatus, string> = {
  PENDING: 'text-yellow-600 bg-yellow-50',
  SUCCESS: 'text-green-600 bg-green-50',
  FAILED: 'text-red-600 bg-red-50',
  CANCELLED: 'text-blue-600 bg-blue-50',
};
