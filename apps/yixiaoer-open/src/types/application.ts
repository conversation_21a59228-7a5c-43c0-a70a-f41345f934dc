export interface Application {
  id: string;
  name: string;
  appId: string;
  secretKey: string;
  description: string;
  status: number;
  applicationType: 'TECHNICAL_SERVICE' | 'CHANNEL_PARTNER';
  availableBalance: number;
  totalAccountPoints: number;
  usedAccountPoints: number;
  totalTraffic: number;
  usedTraffic: number;
  userId: string;
  createdAt: number;
  updatedAt: number;
  userRole: string;
  canManage: boolean;
  userIdentity: string;
  userIdentityLabel: string;
}

export type ApplicationDTO = {
  id?: string;
  name: string;
};

export type ApplicationParams = {
  keyword?: string;
  status?: number;
};

export const applicationTypeMap = {
  TECHNICAL_SERVICE: '技术服务商',
  CHANNEL_PARTNER: '渠道商',
} as const;

export interface ApplicationOverview {
  availableBalance: number;
  totalAccountPoints: number;
  usedAccountPoints: number;
  totalTraffic: number;
  usedTraffic: number;
  totalUsers: number;
  totalTeams: number;
  dailyStats: {
    date: string;
    newTraffic: number;
    usedTraffic: number;
    newAccountPoints: number;
    usedAccountPoints: number;
  }[];
}
