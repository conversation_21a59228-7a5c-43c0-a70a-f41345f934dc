import { OverviewType } from '@/types/overview';
import { makeRequest } from '.';
// 获取 TOP 10
// GET /overview/top-ten
// 接口ID：189759276
// 接口地址：https://app.apifox.com/project/4645177/apis/api-189759276

export const getTopTen = (type: number) => {
  return makeRequest<OverviewType>({
    url: '/overview/top-ten',
    method: 'GET',
    params: {
      type,
    },
  });
};

// 获取团队日活
//   GET /teams/dau
//   接口ID：216470789
//   接口地址：https://app.apifox.com/link/project/4645177/apis/api-216470789
export const getTeamsDau = () =>
  makeRequest<{ list: { data: string; count: number }[] }>({
    url: '/teams/dau',
    method: 'GET',
  });

// 获取收益趋势
// GET /overview/income
// 接口ID：219272115
// 接口地址：https://app.apifox.com/link/project/4645177/apis/api-219272115
export const getIncomeDau = (type: number) => {
  return makeRequest<{ date: string; income: number; expense: number }[]>({
    url: '/overview/income',
    method: 'GET',
    params: {
      type,
    },
  });
};

// 获取30天注册人数
// GET /overview/register
// 接口ID：222748702
// 接口地址：https://app.apifox.com/link/project/4645177/apis/api-222748702
export const getRegisterDau = () =>
  makeRequest<{ createTime: string; registerCount: number }[]>({
    url: '/overview/register',
    method: 'GET',
  });
