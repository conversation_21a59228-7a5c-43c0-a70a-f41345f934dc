import { makeRequest } from './index';
import { PageQuery, PageData } from '@/types/index';
import { TeamsReq, Team, TeamConfigInput } from '@/types/team';

/**
 * 获取团队列表
 * GET /teams
 */
export const teamList = (appId: string, params: PageQuery & TeamsReq) => {
  return makeRequest<PageData<Team>>({
    url: `/gateway/users/app/${appId}/teams`,
    method: 'GET',
    params,
  });
};

/**
 * 设置团队信息
 * PATCH /teams/{id}/config
 */
export const setTeamConfig = (input: TeamConfigInput) => {
  return makeRequest<object>({
    url: `/teams/${input.id}/config`,
    method: 'PATCH',
    data: {
      enabled: input.enabled,
      capacity: input.capacity,
      memberCountLimit: input.memberCountLimit,
      accountCountLimit: input.accountCountLimit,
      expiredAt: input.expiredAt,
    },
  });
};

// 获取团队成员列表数据
//   GET /teams/{teamId}/members
//   接口ID：*********
//   接口地址：https://app.apifox.com/link/project/5710261/apis/api-*********
export const getTeamMembers = (teamId: string) => {
  return makeRequest<
    {
      avatar: string;
      id: string;
      nickName: string;
      phone: string;
    }[]
  >({
    url: `/teams/${teamId}/members`,
    method: 'GET',
  });
};

// 获取团队媒体账号列表数据
//   GET /teams/{teamId}/accounts
//   接口ID：*********
//   接口地址：https://app.apifox.com/link/project/5710261/apis/api-*********
export const getTeamAccounts = (teamId: string) => {
  return makeRequest<
    {
      platformAccountName: string;
      platformAvatar: string;
      platformName: string;
      parentId?: string;
    }[]
  >({
    url: `/teams/${teamId}/accounts`,
    method: 'GET',
  });
};
