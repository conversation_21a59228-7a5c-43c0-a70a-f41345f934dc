// 用户注册/登录
// POST /users/auth
// 接口ID：167862365

import {
  Member,
  User,
  UserInfo,
  UserLogin,
  UserLoginReq,
  UserParams,
  UserSendCodeReq,
} from '@/types/user';
import { makeRequest } from '.';
import { PageData, PageQuery } from '@/types';

// 发送验证码
// POST /open-platform/auth/send-code
// 接口ID：306139748
// 接口地址：https://app.apifox.com/link/project/5710261/apis/api-306139748
export const userSendCode = (params: UserSendCodeReq) => {
  return makeRequest<string>({
    url: '/open-platform/auth/send-code',
    method: 'POST',
    data: params,
  });
};

// 统一认证（登录/注册）
// POST /open-platform/auth/auth
// 接口ID：306321878
// 接口地址：https://app.apifox.com/link/project/5710261/apis/api-306321878
export const userAuth = (params: UserLoginReq) => {
  return makeRequest<UserLogin>({
    url: '/open-platform/auth/auth',
    method: 'POST',
    data: params,
  });
};

// 获取会员列表
// GET /members
// 接口ID：250805458
// 接口地址：https://app.apifox.com/link/project/5710261/apis/api-250805458
export const memberList = (appId: string, params: PageQuery) => {
  return makeRequest<PageData<Member>>({
    url: `/gateway/users/app/${appId}/users`,
    method: 'GET',
    params,
  });
};

// 获取用户信息
// GET /users/info
// 接口ID：167862057
// 接口地址：https://app.apifox.com/project/4330611/apis/api-167862057
export const userInfo = () => {
  return makeRequest<UserInfo>({
    url: '/users/info',
    method: 'GET',
  });
};

// 退出登录
// POST /open-platform/auth/logout
// 接口ID：183259867
// 接口地址：https://www.apifox.cn/project/4645177/apis/api-183259867
export const userLogout = () => {
  return makeRequest({
    url: '/open-platform/auth/logout',
    method: 'POST',
  });
};
// 更新用户信息
// PATCH /users/info
// 接口ID：167862058
// 接口地址：https://app.apifox.com/project/4330611/apis/api-167862058

export const userInfoUpdate = (params: Partial<UserInfo>) => {
  return makeRequest({
    url: '/users/info',
    method: 'PATCH',
    data: params,
  });
};

// 获取用户列表
export const getUsers = (params: PageQuery & UserParams) => {
  return makeRequest<PageData<User>>({
    url: '/members',
    method: 'GET',
    params,
  });
};

// 设置渠道
// PUT /members/{id}/channels
// 接口ID：228291212
// 接口地址：https://app.apifox.com/link/project/4645177/apis/api-228291212
export const setChannel = (id: number, channelCode: string) => {
  return makeRequest({
    url: `/members/${id}/channels`,
    method: 'PUT',
    data: {
      channelCode: channelCode,
    },
  });
};

// 删除用户（注销用户）
// DELETE /members/{id}
// 接口ID：230988889
// 接口地址：https://app.apifox.com/link/project/4645177/apis/api-230988889
export const deleteUser = (id: number, phone: string) => {
  return makeRequest({
    url: `/members/${id}`,
    method: 'DELETE',
    params: {
      phone,
    },
  });
};

// 创建用户（添加用户）
// POST /gateway/users/app/{appId}/users
export const createUser = (
  appId: string,
  params: { phone: string; password: string }
) => {
  return makeRequest({
    url: `/gateway/users/open-platform`,
    method: 'POST',
    data: { ...params, applicationId: appId },
  });
};
