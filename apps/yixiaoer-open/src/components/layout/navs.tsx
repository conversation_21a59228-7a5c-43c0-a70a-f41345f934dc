import {
  ShoppingCart,
  Users2,
  CircleUserRound,
  Users,
  Settings,
} from 'lucide-react';

// 应用-导航数据 概览 | 用户 | 团队 | 订单 | 应用设置
export const getNavs = (applicationId: string, applicationType?: string) => {
  const baseNavs = [
    {
      name: '概览',
      icon: <CircleUserRound className="h-5 w-5" />,
      path: `/application/${applicationId}/overview`,
    },
    {
      name: '用户',
      icon: <Users2 className="h-5 w-5" />,
      path: `/application/${applicationId}/user`,
    },
    {
      name: '团队',
      icon: <Users className="h-5 w-5" />,
      path: `/application/${applicationId}/team`,
    },
    {
      name: '订单',
      icon: <ShoppingCart className="h-5 w-5" />,
      path: `/application/${applicationId}/order`,
    },
  ];

  // 只有技术服务商才显示应用设置
  if (applicationType === 'TECHNICAL_SERVICE') {
    baseNavs.push({
      name: '应用设置',
      icon: <Settings className="h-5 w-5" />,
      path: `/application/${applicationId}/settings`,
    });
  }

  return baseNavs;
};
