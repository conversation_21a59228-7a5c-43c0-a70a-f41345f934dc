import { Search } from 'lucide-react';
import { Input } from '@mono/ui/input';
import { cn } from '@/lib/utils';
import { debounce } from 'lodash';

export default function SearchInput({
  className,
  onSearch,
  onChange,
  ...props
}: React.ComponentProps<typeof Input> & {
  onSearch?: (value: string) => void;
}) {
  // onsearch防抖处理
  const debounceSearch = onSearch ? debounce(onSearch, 300) : undefined;
  return (
    <div className={cn('relative ml-auto flex-1', className)}>
      <Search className="absolute left-2 top-2 h-4 w-4 text-muted-foreground" />
      <Input
        type="search"
        placeholder="Search..."
        className="bg-background pl-8 w-full h-8"
        onChange={(e) => {
          onChange?.(e);
          debounceSearch?.(e.target.value);
        }}
        {...props}
      />
    </div>
    // <div className={cn("px-1.5 flex items-center rounded-md border border-input text-gray-500 gap-2", className)}>
    //    <Search className="h-4 w-4" />
    //    <Input className="flex-1 px-0 border-none shadow-none focus-visible:ring-0" type="search" {...props}/>
    // </div>
  );
}
