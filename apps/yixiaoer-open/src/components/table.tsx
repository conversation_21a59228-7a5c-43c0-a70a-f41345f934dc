import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@mono/ui/table';
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  useReactTable,
  getPaginationRowModel,
  PaginationState,
  OnChangeFn,
} from '@tanstack/react-table';
import { DataTablePagination } from '@/components/dataTablePagination';
import { Card } from '@mono/ui/card';
import { LoadingContainer } from './loading';

interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[];
  data?: TData[];
  rowCount?: number;
  pagination: PaginationState;
  setPagination: OnChangeFn<PaginationState>;
  onItemClick?: (item: TData) => void;
}

export default function TableData<TData, TValue>({
  columns,
  data,
  rowCount,
  pagination,
  setPagination,
  onItemClick,
}: DataTableProps<TData, TValue>) {
  const table = useReactTable({
    data: data ?? [],
    columns,
    rowCount,
    state: {
      pagination,
    },
    onPaginationChange: setPagination,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    manualPagination: true,
    // debugTable: true,
    // defaultColumn: {
    //   size: 200,
    //   minSize: 200,
    //   maxSize: 200,
    // },
    // autoResetPageIndex: true,
  });
  return (
    <Card className="flex flex-col flex-1 overflow-hidden justify-between gap-2">
      {data ?
        <Table>
          <TableHeader className="sticky text-[#222222] top-0 bg-muted z-10">
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id} className="font-bold px-5">
                      {header.isPlaceholder ? null : (
                        flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )
                      )}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ?
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && 'selected'}
                  onClick={() => onItemClick?.(row.original)}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell
                      align="left"
                      key={cell.id}
                      className="px-5 text-[#222222]"
                      style={{
                        width: cell.column.getSize(),
                      }}
                    >
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            : <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center px-5"
                  style={{ color: '#222222' }}
                >
                  No results.
                </TableCell>
              </TableRow>
            }
          </TableBody>
        </Table>
      : <LoadingContainer />}
      <div className="flex-shrink-0 pb-2">
        <DataTablePagination table={table} />
      </div>
    </Card>
  );
}
