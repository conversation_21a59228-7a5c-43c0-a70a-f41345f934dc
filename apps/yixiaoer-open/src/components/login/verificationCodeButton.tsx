import { Button } from '@mono/ui/button';
import { useEffect, useMemo, useRef, useState } from 'react';
import { useFormContext, useWatch } from 'react-hook-form';
import { useMutation } from '@tanstack/react-query';
import { userSendCode } from '@/api/user';
import { LoaderCircle } from 'lucide-react';
import { phoneRegex } from '@/lib/utils';
import { toast } from 'sonner';

export function VerificationCodeButton({
  onVerifyCode,
}: {
  onVerifyCode?: (code: string) => void;
}) {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  let captcha: unknown = null;
  const [countdown, setCountdown] = useState(0); // 倒计时秒数
  const [isButtonDisabled, setIsButtonDisabled] = useState(false); // 按钮状态
  const phoneRef = useRef<string>('');

  const { control } = useFormContext();
  const phone = useWatch({ control, name: 'phone' });

  const mutation = useMutation({
    mutationFn: userSendCode,
    onSuccess: (res) => {
      // console.log('res:\n', res);
      setIsButtonDisabled(true); // 禁用按钮
      setCountdown(60); // 开始 60 秒倒计时
      if (res) {
        toast.info(`测试验证码：${res}`);
        onVerifyCode?.(res);
      }
      // 发送验证码成功
      console.log('发送验证码成功');
    },
    onError: (error) => {
      // 发送验证码失败
      console.log(error);
    },
  });

  const disabled = useMemo(
    () => !phoneRegex.test(phone) || isButtonDisabled,
    [phone, isButtonDisabled]
  );

  // 倒计时逻辑
  useEffect(() => {
    if (countdown > 0) {
      const timer = setInterval(() => {
        setCountdown((prev) => prev - 1);
      }, 1000); // 每秒更新一次
      return () => clearInterval(timer); // 清除定时器
    } else {
      setIsButtonDisabled(false); // 倒计时结束，启用按钮
    }
  }, [countdown]); // 当 countdown 变化时运行

  useEffect(() => {
    // 每当phone更新时同步到ref
    phoneRef.current = phone;
  }, [phone]);

  const getInstance = (instance: unknown) => {
    captcha = instance;
    console.debug('captcha:', captcha);
  };

  // 验证码验证回调函数-业务请求(带验证码校验)回调函数
  const captchaVerifyCallback = async (captchaVerifyParam: string) => {
    try {
      await mutation.mutateAsync({
        phone: phoneRef.current,
        captchaVerifyParam: captchaVerifyParam,
      });

      return {
        captchaResult: true,
        bizResult: true,
      };
    } catch (error) {
      return {
        captchaResult: false,
        bizResult: false,
      };
    }
  };

  // 验证通过后调用
  const onBizResultCallback = async (value: boolean) => {
    if (!value) return;
    // requestVerificationCode(); // 发送验证码请求
  };

  useEffect(() => {
    const win = window as { initAliyunCaptcha?: (config: unknown) => void };
    if (typeof win.initAliyunCaptcha === 'function') {
      win.initAliyunCaptcha({
        SceneId: '5aun0gpl', // 场景ID。根据步骤二新建验证场景后，您可以在验证码场景列表，获取该场景的场景ID
        prefix: '1e2gh5', // 身份标。开通阿里云验证码2.0后，您可以在控制台概览页面的实例基本信息卡片区域，获取身份标
        mode: 'popup', // 验证码模式。popup表示要集成的验证码模式为弹出式。无需修改
        element: '#captcha-element', // 页面上预留的渲染验证码的元素，与原代码中预留的页面元素保持一致。
        button: '#captcha-button', // 触发验证码弹窗的元素。button表示单击登录按钮后，触发captchaVerifyCallback函数。您可以根据实际使用的元素修改element的值
        captchaVerifyCallback: captchaVerifyCallback, // 业务请求(带验证码校验)回调函数，无需修改
        onBizResultCallback: onBizResultCallback, // 业务请求结果回调函数，无需修改
        getInstance: getInstance, // 绑定验证码实例函数，无需修改
        slideStyle: {
          width: 360,
          height: 40,
        }, // 滑块验证码样式，支持自定义宽度和高度，单位为px。其中，width最小值为320 px
        language: 'cn', // 验证码语言类型，支持简体中文（cn）、繁体中文（tw）、英文（en）
      });
    }
    return () => {
      // 必须删除相关元素，否则再次mount多次调用 initAliyunCaptcha 会导致多次回调 captchaVerifyCallback
      document.getElementById('aliyunCaptcha-mask')?.remove();
      document.getElementById('aliyunCaptcha-window-popup')?.remove();
    };
  }, []);

  return (
    <>
      <div className="absolute right-2 h-full top-0 flex justify-center items-center">
        <Button
          id="captcha-button"
          disabled={disabled || mutation.isPending}
          type="button"
          className="px-2"
          variant="ghost"
        >
          {mutation.isPending && (
            <LoaderCircle className="mr-2 h-4 w-4 animate-spin" />
          )}
          {isButtonDisabled ? `重新获取 (${countdown}s)` : '获取验证码'}
        </Button>
      </div>
      <div id="captcha-element"></div>
    </>
  );
}
