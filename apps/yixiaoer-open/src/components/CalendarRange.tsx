import { Button } from '@mono/ui/button';
import { FormControl } from '@mono/ui/form';
import { Popover, PopoverContent, PopoverTrigger } from '@mono/ui/popover';
import { cn } from '@/lib/utils';
import { CalendarIcon, X } from 'lucide-react';
import { formatYearMonthDay } from '@/lib/utils/day';
import { Calendar } from '@mono/ui/calendar';
import { zhCN } from 'date-fns/locale';

export function CalendarRange({
  value,
  onChange,
}: {
  value?: { from: Date; to?: Date };
  onChange?: (value?: { from?: Date; to?: Date }) => void;
}) {
  const handleClear = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    onChange?.(undefined);
  };

  return (
    <div className="relative">
      <Popover>
        <PopoverTrigger asChild>
          <FormControl>
            <Button
              variant={'outline'}
              className={cn(
                'w-[220px] pl-3 pr-8 text-left font-normal relative',
                !(value && value.from) && 'text-muted-foreground'
              )}
            >
              <span className={value && value.from ? 'pr-6' : ''}>
                {value && value.from ?
                  value.to ?
                    <>
                      {formatYearMonthDay(value.from)} -{' '}
                      {formatYearMonthDay(value.to)}
                    </>
                  : formatYearMonthDay(value.from)
                : <span>选择时间段</span>}
              </span>
              <CalendarIcon className="absolute right-2 top-1/2 transform -translate-y-1/2 h-4 w-4 opacity-50" />
            </Button>
          </FormControl>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="start">
          <Calendar
            locale={zhCN}
            mode="range"
            selected={value}
            onSelect={onChange}
            numberOfMonths={2}
            initialFocus
          />
        </PopoverContent>
      </Popover>

      {value && value.from ?
        <X
          className="absolute right-6 top-1/2 transform -translate-y-1/2 h-4 w-4 opacity-50 hover:opacity-100 cursor-pointer z-10"
          onClick={handleClear}
        />
      : null}
    </div>
  );
}
