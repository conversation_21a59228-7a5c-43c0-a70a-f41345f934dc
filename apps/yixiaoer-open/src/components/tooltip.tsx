import { cn } from '@/lib/utils';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@mono/ui/tooltip';

const SauryTooltip = ({
  children,
  tooltip,
  side = 'top',
  className,
  ...props
}: {
  children: React.ReactNode;
  tooltip: React.ReactNode;
  side?: React.ComponentProps<typeof TooltipContent>['side'];
  className?: string;
} & React.ComponentProps<typeof Tooltip>) => (
  <TooltipProvider delayDuration={200}>
    <Tooltip {...props}>
      <TooltipTrigger asChild>{children}</TooltipTrigger>
      <TooltipContent side={side} className={cn('max-w-96', className)}>
        {tooltip}
      </TooltipContent>
    </Tooltip>
  </TooltipProvider>
);

export default SauryTooltip;
