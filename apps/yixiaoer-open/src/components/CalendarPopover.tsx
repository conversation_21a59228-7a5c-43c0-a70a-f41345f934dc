import { But<PERSON> } from '@mono/ui/button';
import { FormControl } from '@mono/ui/form';
import { Popover, PopoverContent, PopoverTrigger } from '@mono/ui/popover';
import { cn } from '@/lib/utils';
import { CalendarIcon } from 'lucide-react';
import { formatYearMonthDay } from '@/lib/utils/day';
import { Calendar } from '@mono/ui/calendar';
import { zhCN } from 'date-fns/locale';

export function CalendarPopover({
  value,
  onChange,
  placeHolder = '选择时间',
}: {
  value?: Date;
  onChange?: (value?: Date) => void;
  placeHolder?: string;
}) {
  return (
    <Popover>
      <PopoverTrigger asChild>
        <FormControl>
          <Button
            variant={'outline'}
            className={cn(
              'w-full text-left font-normal',
              !value && 'text-muted-foreground'
            )}
          >
            {value ? formatYearMonthDay(value) : <span>{placeHolder}</span>}
            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
          </Button>
        </FormControl>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0" align="start">
        <Calendar
          locale={zhCN}
          mode="single"
          selected={value}
          onSelect={onChange}
          initialFocus
        />
      </PopoverContent>
    </Popover>
  );
}
