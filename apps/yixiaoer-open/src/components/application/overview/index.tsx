import { ScrollArea } from '@mono/ui/scroll-area';
import Charts from './charts';
import YiBei from '@/assets/yibei.svg';
import Copy from '@/assets/copy.svg';
import { HelpTooltop } from '@/components/helpTooltop';
import { formatNumber } from '@/lib/utils';
import { Button } from '@mono/ui/button';
import { toast } from 'sonner';
import {
  useApplicationDetail,
  useApplicationOverview,
} from '@/hooks/useApplicationDetail';
import { RechargeModal } from './rechargeModal';
import { useState } from 'react';
import { useNavigate, useParams } from '@tanstack/react-router';

export function ApplicationOverview() {
  const { data } = useApplicationDetail();
  const { data: overview } = useApplicationOverview();
  const [showRechargeModal, setShowRechargeModal] = useState(false);
  const navigate = useNavigate();
  const params = useParams({ strict: false });
  const applicationId = params.applicationId;

  // 复制方法
  const copy = (text: string) => {
    navigator.clipboard.writeText(text);
    toast.success('复制成功');
  };

  // 跳转到订单管理页面
  const navigateToOrders = () => {
    navigate({ to: `/application/${applicationId}/order` });
  };

  return (
    <ScrollArea className="w-full h-full">
      <div className="h-full flex flex-col items-center gap-4 p-5">
        <div className="w-full max-w-none flex flex-col gap-4 bg-white p-5 rounded-lg shadow-[0px_0px_6px_0px_rgba(0,0,0,0.02)]">
          <p className="text-[18px] text-[#222222] font-medium leading-6">
            应用信息
          </p>

          <div className="flex flex-col gap-2 p-5 border border-[#F1F1F1] bg-[#FAFAFC] rounded-md">
            <div className="flex">
              <span className="w-[98px] text-sm text-[#757575] font-normal leading-[22px]">
                应用名称：
              </span>
              <span className="text-sm text-[#222222] font-medium leading-[22px]">
                {data?.name}
              </span>

              <img
                src={Copy}
                alt=""
                className="ml-2 cursor-pointer"
                onClick={() => copy(data?.appId || '')}
              />
            </div>
            {data?.applicationType === 'TECHNICAL_SERVICE' && (
              <div>
                <div className="flex">
                  <span className="w-[98px] text-sm text-[#757575] font-normal leading-[22px]">
                    应用ID：
                  </span>
                  <span className="text-sm text-[#222222] font-medium leading-[22px]">
                    {data?.appId}
                  </span>

                  <img
                    src={Copy}
                    alt=""
                    className="ml-2 cursor-pointer"
                    onClick={() => copy(data?.appId || '')}
                  />
                </div>
                <div className="flex">
                  <span className="w-[98px] text-sm text-[#757575] font-normal leading-[22px]">
                    Secret：
                  </span>
                  <span className="text-sm text-[#222222] font-medium leading-[22px]">
                    {data?.secretKey}
                  </span>

                  <img
                    src={Copy}
                    alt=""
                    className="ml-2 cursor-pointer"
                    onClick={() => copy(data?.secretKey || '')}
                  />
                </div>
              </div>
            )}
          </div>
        </div>

        <div className="w-full max-w-none flex flex-col bg-white p-5 rounded-lg shadow-[0px_0px_6px_0px_rgba(0,0,0,0.02)]">
          <p className="text-[18px] text-[#222222] font-medium leading-6">
            数据
          </p>

          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-3 pt-5 pb-6">
            <div className="h-[90px] flex flex-col gap-2.5 justify-center pl-6 pr-[19px] bg-[#F8F8FA] rounded-lg ">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-1">
                  <img src={YiBei} alt="" className="w-5 h-5" />
                  <span className="text-sm text-[#54555E] font-medium">
                    蚁贝
                  </span>
                  <HelpTooltop
                    tooltipClassName="max-w-max"
                    title={
                      <div className="flex flex-col gap-1 p-1">
                        <span>
                          蚁贝是您在蚁小二开放平台的虚拟资产，用来购买账号额度、流量。
                        </span>
                        <span>资产比例为：1（元）：1（蚁贝）</span>
                        <span>账号额度：1账号/40蚁贝，流量：1GB/1蚁贝</span>
                      </div>
                    }
                  />
                </div>

                <span
                  className="text-xs text-[#4F46E5] cursor-pointer hover:underline"
                  onClick={navigateToOrders}
                >
                  明细
                </span>
              </div>

              <div className="flex items-center gap-2">
                <span className="text-[20px] text-[#222222] font-extrabold leading-[26px]">
                  {formatNumber(overview?.availableBalance ?? 0)}
                </span>

                <Button
                  variant="outline"
                  className="h-max p-0 px-3 text-xs text-[#4F46E5] hover:text-[#4F46E5] hover:bg-[rgba(78,62,233,0.1)] font-normal leading-[22px] rounded-xl border border-[rgba(78,62,233,0.3)]"
                  onClick={() => setShowRechargeModal(true)}
                >
                  充值
                </Button>
              </div>
            </div>

            <div className="h-[90px] flex flex-col gap-2.5 justify-center pl-6 pr-[19px] bg-[#F8F8FA] rounded-lg ">
              <span className="text-sm text-[#54555E] font-medium">
                帐号点数
              </span>

              <span className="text-[20px] text-[#222222] font-extrabold leading-[26px]">
                {formatNumber(overview?.totalAccountPoints ?? 0)}
              </span>
            </div>

            <div className="h-[90px] flex flex-col gap-2.5 justify-center pl-6 pr-[19px] bg-[#F8F8FA] rounded-lg ">
              <span className="text-sm text-[#54555E] font-medium">流量</span>

              <span className="text-[20px] text-[#222222] font-extrabold leading-[26px]">
                {overview?.usedTraffic}
                <span className="text-[#757575]">
                  /{overview ? overview.totalTraffic : '-'}
                </span>
              </span>
            </div>

            <div className="h-[90px] flex flex-col gap-2.5 justify-center pl-6 pr-[19px] bg-[#F8F8FA] rounded-lg ">
              <span className="text-sm text-[#54555E] font-medium">用户</span>

              <span className="text-[20px] text-[#222222] font-extrabold leading-[26px]">
                {formatNumber(overview?.totalUsers ?? 0)}
              </span>
            </div>

            <div className="h-[90px] flex flex-col gap-2.5 justify-center pl-6 pr-[19px] bg-[#F8F8FA] rounded-lg ">
              <span className="text-sm text-[#54555E] font-medium">团队</span>

              <span className="text-[20px] text-[#222222] font-extrabold leading-[26px]">
                {formatNumber(overview?.totalTeams ?? 0)}
              </span>
            </div>
          </div>

          <div className="flex flex-col gap-7 p-5 border border-[#E8EAEC] rounded-[10px] min-h-[500px]">
            <p className="text-base text-[#222222] font-medium leading-[22px]">
              近30日配额数据
            </p>

            <div className="flex-1 min-h-[400px]">
              <Charts list={overview?.dailyStats ?? []} />
            </div>
          </div>
        </div>
      </div>

      {/* 充值弹窗 */}
      <RechargeModal
        open={showRechargeModal}
        onClose={() => setShowRechargeModal(false)}
      />
    </ScrollArea>
  );
}
