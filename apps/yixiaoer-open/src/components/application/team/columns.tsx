import { ColumnDef } from '@tanstack/react-table';
import { formatDate } from '@/lib/utils/day';
import { Team } from '@/types/team';
import { Badge } from '@mono/ui/badge';

export function getTableColumns(): Array<ColumnDef<Team>> {
  return [
    {
      header: '团队信息',
      accessorKey: 'teamInfo',
      cell: ({ row }) => {
        const { name, code } = row.original;
        return (
          <div className="flex flex-col text-muted-foreground">
            <span>团队名称：{name}</span>
            <span>团队ID：{code}</span>
          </div>
        );
      },
    },
    {
      header: '创建时间',
      accessorKey: '',
      cell: ({ row }) => {
        const { createdAt } = row.original;
        return (
          <span className="text-muted-foreground">{formatDate(createdAt)}</span>
        );
      },
    },
    {
      header: '授权账号',
      accessorKey: '',
      cell: ({ row }) => {
        const { accountCapacity, accountCapacityLimit } = row.original;
        return (
          <span className="text-muted-foreground">
            {accountCapacity}/{accountCapacityLimit}
          </span>
        );
      },
    },
    {
      header: '团队流量',
      accessorKey: '',
      cell: ({ row }) => {
        const { useNetworkTraffic, networkTraffic } = row.original;
        return (
          <span className="text-muted-foreground">
            {useNetworkTraffic}/{networkTraffic}
          </span>
        );
      },
    },
    {
      header: '团队成员',
      accessorKey: 'memberCount',
      cell: ({ row }) => {
        const { memberCount } = row.original;
        return <span className="text-muted-foreground">{memberCount}</span>;
      },
    },
    {
      header: 'VIP状态',
      accessorKey: 'enabled',
      cell: ({ row }) => {
        const { expiredAt } = row.original;
        const isExpired = expiredAt && new Date(expiredAt) > new Date(); // 检查 expiredAt 是否为 null 或有效
        return (
          <span>
            {isExpired ?
              <Badge variant="default">已开通</Badge>
            : <Badge variant="secondary">未开通</Badge>}
          </span>
        );
      },
    },
  ];
}
