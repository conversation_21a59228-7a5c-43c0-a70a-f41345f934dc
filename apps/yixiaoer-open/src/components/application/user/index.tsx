import TableData from '@/components/table';
import { useQuery } from '@tanstack/react-query';
import { PaginationState } from '@tanstack/react-table';
import { useMemo, useState } from 'react';
import { memberList } from '@/api/user';
import { getTableColumns } from './columns';
import { useParams } from '@tanstack/react-router';
import { useApplicationDetail } from '@/hooks/useApplicationDetail';
import { Button } from '@mono/ui/button';
import { Plus } from 'lucide-react';
import { AddUserModal } from './AddUserModal';

export function ApplicationUser() {
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 20,
  });
  const [showAddUserModal, setShowAddUserModal] = useState(false);

  const params = useParams({ strict: false });
  const applicationId = params.applicationId;

  // 获取应用详情以判断应用类型
  const { data: applicationDetail } = useApplicationDetail();

  const query = useQuery({
    queryKey: ['memberList', pagination, applicationId],
    queryFn: () =>
      memberList(applicationId as string, {
        size: pagination.pageSize,
        page: pagination.pageIndex + 1,
      }),
    enabled: !!applicationId,
  });

  const columns = useMemo(() => {
    return getTableColumns();
  }, []);

  // 判断是否为渠道商应用
  const isChannelPartner =
    applicationDetail?.applicationType === 'CHANNEL_PARTNER';

  return (
    <div className="w-full h-full flex flex-col px-[20px] py-5 overflow-hidden">
      {/* 工具栏 */}
      {isChannelPartner && (
        <div className="flex justify-end mb-4">
          <Button
            onClick={() => setShowAddUserModal(true)}
            className="flex items-center gap-2"
          >
            <Plus className="w-4 h-4" />
            添加用户
          </Button>
        </div>
      )}

      <TableData
        columns={columns}
        data={query.data?.data || []}
        rowCount={query.data?.totalSize ?? 0}
        pagination={pagination}
        setPagination={setPagination}
      />

      {/* 添加用户弹窗 */}
      {isChannelPartner && (
        <AddUserModal
          open={showAddUserModal}
          onClose={() => setShowAddUserModal(false)}
          applicationId={applicationId as string}
        />
      )}
    </div>
  );
}
