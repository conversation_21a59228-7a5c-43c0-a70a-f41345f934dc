import { router } from '@/router';
import { useUserStore } from '@/store/user';

/**
 * 退出登录，清除所有用户数据并跳转到登录页面
 */
export const logout = () => {
  // 使用 store 的 clearUser 方法清除用户数据
  useUserStore.getState().clearUser();

  // 清除 localStorage 中的持久化数据
  // Zustand persist 会自动处理，但为了确保清除，我们手动清除
  localStorage.removeItem('yixiaoer-open-user');

  // 清除其他可能的认证相关数据
  localStorage.removeItem('token');
  localStorage.removeItem('accessToken');
  localStorage.removeItem('refreshToken');

  // 清除可能的会话存储数据
  sessionStorage.clear();

  // 跳转到登录页面
  router.navigate({ to: '/login', replace: true });
};
