import { Command, ServiceMessage } from '@/types/service-message';

// 处理消息队列，收到消息后，将消息放入消息队列，同步执行消息队列
const msgQueue: ServiceMessage[] = [];
let msgQueueLock = false;
export async function handlerMsgQueue(msg: ServiceMessage) {
  msgQueue.push(msg);
  if (msgQueueLock) {
    return;
  }
  msgQueueLock = true;
  while (msgQueue.length > 0) {
    await handlerMsg(msgQueue.shift()!);
  }
  msgQueueLock = false;
}

// 处理 msg socket
export async function handlerMsg(msg: ServiceMessage) {
  try {
    if ('type' in msg) {
      const commandMsg = msg as Command<unknown>;
      console.log('commandMsg:\n', commandMsg);
    }
  } catch (error) {
    console.error('handlerMsg error', error);
  }
}
