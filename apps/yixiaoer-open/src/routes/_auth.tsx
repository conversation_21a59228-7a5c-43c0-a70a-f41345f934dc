import { Header } from '@/components/layout/header';
import { useUserStore } from '@/store/user';
import { Outlet, createFileRoute, redirect } from '@tanstack/react-router';

export const Route = createFileRoute('/_auth')({
  beforeLoad: ({ location }) => {
    if (!useUserStore.getState().user) {
      throw redirect({
        to: '/login',
        search: {
          redirect: location.href,
        },
      });
    }
  },
  component: AuthLayout,
});

function AuthLayout() {
  return (
    <div className="h-full w-full flex flex-col">
      <Header />
      <main className="flex-1 h-full p-0 overflow-hidden">
        <Outlet />
      </main>
    </div>
  );
}
