/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

// Import Routes

import { Route as rootRoute } from './routes/__root';
import { Route as LoginImport } from './routes/login';
import { Route as AuthImport } from './routes/_auth';
import { Route as IndexImport } from './routes/index';
import { Route as AuthRechargeManagementImport } from './routes/_auth/recharge-management';
import { Route as AuthConsoleBoardImport } from './routes/_auth/console-board';
import { Route as AuthApplicationImport } from './routes/_auth/application';
import { Route as AuthApplicationDetailImport } from './routes/_auth/application/detail';
import { Route as AuthApplicationApplicationIdUserImport } from './routes/_auth/application/$applicationId.user';
import { Route as AuthApplicationApplicationIdTeamImport } from './routes/_auth/application/$applicationId.team';
import { Route as AuthApplicationApplicationIdSettingsImport } from './routes/_auth/application/$applicationId.settings';
import { Route as AuthApplicationApplicationIdOverviewImport } from './routes/_auth/application/$applicationId.overview';
import { Route as AuthApplicationApplicationIdOrderImport } from './routes/_auth/application/$applicationId.order';

// Create/Update Routes

const LoginRoute = LoginImport.update({
  id: '/login',
  path: '/login',
  getParentRoute: () => rootRoute,
} as any);

const AuthRoute = AuthImport.update({
  id: '/_auth',
  getParentRoute: () => rootRoute,
} as any);

const IndexRoute = IndexImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRoute,
} as any);

const AuthRechargeManagementRoute = AuthRechargeManagementImport.update({
  id: '/recharge-management',
  path: '/recharge-management',
  getParentRoute: () => AuthRoute,
} as any);

const AuthConsoleBoardRoute = AuthConsoleBoardImport.update({
  id: '/console-board',
  path: '/console-board',
  getParentRoute: () => AuthRoute,
} as any);

const AuthApplicationRoute = AuthApplicationImport.update({
  id: '/application',
  path: '/application',
  getParentRoute: () => AuthRoute,
} as any);

const AuthApplicationDetailRoute = AuthApplicationDetailImport.update({
  id: '/detail',
  path: '/detail',
  getParentRoute: () => AuthApplicationRoute,
} as any);

const AuthApplicationApplicationIdUserRoute =
  AuthApplicationApplicationIdUserImport.update({
    id: '/$applicationId/user',
    path: '/$applicationId/user',
    getParentRoute: () => AuthApplicationRoute,
  } as any);

const AuthApplicationApplicationIdTeamRoute =
  AuthApplicationApplicationIdTeamImport.update({
    id: '/$applicationId/team',
    path: '/$applicationId/team',
    getParentRoute: () => AuthApplicationRoute,
  } as any);

const AuthApplicationApplicationIdSettingsRoute =
  AuthApplicationApplicationIdSettingsImport.update({
    id: '/$applicationId/settings',
    path: '/$applicationId/settings',
    getParentRoute: () => AuthApplicationRoute,
  } as any);

const AuthApplicationApplicationIdOverviewRoute =
  AuthApplicationApplicationIdOverviewImport.update({
    id: '/$applicationId/overview',
    path: '/$applicationId/overview',
    getParentRoute: () => AuthApplicationRoute,
  } as any);

const AuthApplicationApplicationIdOrderRoute =
  AuthApplicationApplicationIdOrderImport.update({
    id: '/$applicationId/order',
    path: '/$applicationId/order',
    getParentRoute: () => AuthApplicationRoute,
  } as any);

// Populate the FileRoutesByPath interface

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/': {
      id: '/';
      path: '/';
      fullPath: '/';
      preLoaderRoute: typeof IndexImport;
      parentRoute: typeof rootRoute;
    };
    '/_auth': {
      id: '/_auth';
      path: '';
      fullPath: '';
      preLoaderRoute: typeof AuthImport;
      parentRoute: typeof rootRoute;
    };
    '/login': {
      id: '/login';
      path: '/login';
      fullPath: '/login';
      preLoaderRoute: typeof LoginImport;
      parentRoute: typeof rootRoute;
    };
    '/_auth/application': {
      id: '/_auth/application';
      path: '/application';
      fullPath: '/application';
      preLoaderRoute: typeof AuthApplicationImport;
      parentRoute: typeof AuthImport;
    };
    '/_auth/console-board': {
      id: '/_auth/console-board';
      path: '/console-board';
      fullPath: '/console-board';
      preLoaderRoute: typeof AuthConsoleBoardImport;
      parentRoute: typeof AuthImport;
    };
    '/_auth/recharge-management': {
      id: '/_auth/recharge-management';
      path: '/recharge-management';
      fullPath: '/recharge-management';
      preLoaderRoute: typeof AuthRechargeManagementImport;
      parentRoute: typeof AuthImport;
    };
    '/_auth/application/detail': {
      id: '/_auth/application/detail';
      path: '/detail';
      fullPath: '/application/detail';
      preLoaderRoute: typeof AuthApplicationDetailImport;
      parentRoute: typeof AuthApplicationImport;
    };
    '/_auth/application/$applicationId/order': {
      id: '/_auth/application/$applicationId/order';
      path: '/$applicationId/order';
      fullPath: '/application/$applicationId/order';
      preLoaderRoute: typeof AuthApplicationApplicationIdOrderImport;
      parentRoute: typeof AuthApplicationImport;
    };
    '/_auth/application/$applicationId/overview': {
      id: '/_auth/application/$applicationId/overview';
      path: '/$applicationId/overview';
      fullPath: '/application/$applicationId/overview';
      preLoaderRoute: typeof AuthApplicationApplicationIdOverviewImport;
      parentRoute: typeof AuthApplicationImport;
    };
    '/_auth/application/$applicationId/settings': {
      id: '/_auth/application/$applicationId/settings';
      path: '/$applicationId/settings';
      fullPath: '/application/$applicationId/settings';
      preLoaderRoute: typeof AuthApplicationApplicationIdSettingsImport;
      parentRoute: typeof AuthApplicationImport;
    };
    '/_auth/application/$applicationId/team': {
      id: '/_auth/application/$applicationId/team';
      path: '/$applicationId/team';
      fullPath: '/application/$applicationId/team';
      preLoaderRoute: typeof AuthApplicationApplicationIdTeamImport;
      parentRoute: typeof AuthApplicationImport;
    };
    '/_auth/application/$applicationId/user': {
      id: '/_auth/application/$applicationId/user';
      path: '/$applicationId/user';
      fullPath: '/application/$applicationId/user';
      preLoaderRoute: typeof AuthApplicationApplicationIdUserImport;
      parentRoute: typeof AuthApplicationImport;
    };
  }
}

// Create and export the route tree

interface AuthApplicationRouteChildren {
  AuthApplicationDetailRoute: typeof AuthApplicationDetailRoute;
  AuthApplicationApplicationIdOrderRoute: typeof AuthApplicationApplicationIdOrderRoute;
  AuthApplicationApplicationIdOverviewRoute: typeof AuthApplicationApplicationIdOverviewRoute;
  AuthApplicationApplicationIdSettingsRoute: typeof AuthApplicationApplicationIdSettingsRoute;
  AuthApplicationApplicationIdTeamRoute: typeof AuthApplicationApplicationIdTeamRoute;
  AuthApplicationApplicationIdUserRoute: typeof AuthApplicationApplicationIdUserRoute;
}

const AuthApplicationRouteChildren: AuthApplicationRouteChildren = {
  AuthApplicationDetailRoute: AuthApplicationDetailRoute,
  AuthApplicationApplicationIdOrderRoute:
    AuthApplicationApplicationIdOrderRoute,
  AuthApplicationApplicationIdOverviewRoute:
    AuthApplicationApplicationIdOverviewRoute,
  AuthApplicationApplicationIdSettingsRoute:
    AuthApplicationApplicationIdSettingsRoute,
  AuthApplicationApplicationIdTeamRoute: AuthApplicationApplicationIdTeamRoute,
  AuthApplicationApplicationIdUserRoute: AuthApplicationApplicationIdUserRoute,
};

const AuthApplicationRouteWithChildren = AuthApplicationRoute._addFileChildren(
  AuthApplicationRouteChildren
);

interface AuthRouteChildren {
  AuthApplicationRoute: typeof AuthApplicationRouteWithChildren;
  AuthConsoleBoardRoute: typeof AuthConsoleBoardRoute;
  AuthRechargeManagementRoute: typeof AuthRechargeManagementRoute;
}

const AuthRouteChildren: AuthRouteChildren = {
  AuthApplicationRoute: AuthApplicationRouteWithChildren,
  AuthConsoleBoardRoute: AuthConsoleBoardRoute,
  AuthRechargeManagementRoute: AuthRechargeManagementRoute,
};

const AuthRouteWithChildren = AuthRoute._addFileChildren(AuthRouteChildren);

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute;
  '': typeof AuthRouteWithChildren;
  '/login': typeof LoginRoute;
  '/application': typeof AuthApplicationRouteWithChildren;
  '/console-board': typeof AuthConsoleBoardRoute;
  '/recharge-management': typeof AuthRechargeManagementRoute;
  '/application/detail': typeof AuthApplicationDetailRoute;
  '/application/$applicationId/order': typeof AuthApplicationApplicationIdOrderRoute;
  '/application/$applicationId/overview': typeof AuthApplicationApplicationIdOverviewRoute;
  '/application/$applicationId/settings': typeof AuthApplicationApplicationIdSettingsRoute;
  '/application/$applicationId/team': typeof AuthApplicationApplicationIdTeamRoute;
  '/application/$applicationId/user': typeof AuthApplicationApplicationIdUserRoute;
}

export interface FileRoutesByTo {
  '/': typeof IndexRoute;
  '': typeof AuthRouteWithChildren;
  '/login': typeof LoginRoute;
  '/application': typeof AuthApplicationRouteWithChildren;
  '/console-board': typeof AuthConsoleBoardRoute;
  '/recharge-management': typeof AuthRechargeManagementRoute;
  '/application/detail': typeof AuthApplicationDetailRoute;
  '/application/$applicationId/order': typeof AuthApplicationApplicationIdOrderRoute;
  '/application/$applicationId/overview': typeof AuthApplicationApplicationIdOverviewRoute;
  '/application/$applicationId/settings': typeof AuthApplicationApplicationIdSettingsRoute;
  '/application/$applicationId/team': typeof AuthApplicationApplicationIdTeamRoute;
  '/application/$applicationId/user': typeof AuthApplicationApplicationIdUserRoute;
}

export interface FileRoutesById {
  __root__: typeof rootRoute;
  '/': typeof IndexRoute;
  '/_auth': typeof AuthRouteWithChildren;
  '/login': typeof LoginRoute;
  '/_auth/application': typeof AuthApplicationRouteWithChildren;
  '/_auth/console-board': typeof AuthConsoleBoardRoute;
  '/_auth/recharge-management': typeof AuthRechargeManagementRoute;
  '/_auth/application/detail': typeof AuthApplicationDetailRoute;
  '/_auth/application/$applicationId/order': typeof AuthApplicationApplicationIdOrderRoute;
  '/_auth/application/$applicationId/overview': typeof AuthApplicationApplicationIdOverviewRoute;
  '/_auth/application/$applicationId/settings': typeof AuthApplicationApplicationIdSettingsRoute;
  '/_auth/application/$applicationId/team': typeof AuthApplicationApplicationIdTeamRoute;
  '/_auth/application/$applicationId/user': typeof AuthApplicationApplicationIdUserRoute;
}

export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath;
  fullPaths:
    | '/'
    | ''
    | '/login'
    | '/application'
    | '/console-board'
    | '/recharge-management'
    | '/application/detail'
    | '/application/$applicationId/order'
    | '/application/$applicationId/overview'
    | '/application/$applicationId/settings'
    | '/application/$applicationId/team'
    | '/application/$applicationId/user';
  fileRoutesByTo: FileRoutesByTo;
  to:
    | '/'
    | ''
    | '/login'
    | '/application'
    | '/console-board'
    | '/recharge-management'
    | '/application/detail'
    | '/application/$applicationId/order'
    | '/application/$applicationId/overview'
    | '/application/$applicationId/settings'
    | '/application/$applicationId/team'
    | '/application/$applicationId/user';
  id:
    | '__root__'
    | '/'
    | '/_auth'
    | '/login'
    | '/_auth/application'
    | '/_auth/console-board'
    | '/_auth/recharge-management'
    | '/_auth/application/detail'
    | '/_auth/application/$applicationId/order'
    | '/_auth/application/$applicationId/overview'
    | '/_auth/application/$applicationId/settings'
    | '/_auth/application/$applicationId/team'
    | '/_auth/application/$applicationId/user';
  fileRoutesById: FileRoutesById;
}

export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute;
  AuthRoute: typeof AuthRouteWithChildren;
  LoginRoute: typeof LoginRoute;
}

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  AuthRoute: AuthRouteWithChildren,
  LoginRoute: LoginRoute,
};

export const routeTree = rootRoute
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>();

/* ROUTE_MANIFEST_START
{
  "routes": {
    "__root__": {
      "filePath": "__root.tsx",
      "children": [
        "/",
        "/_auth",
        "/login"
      ]
    },
    "/": {
      "filePath": "index.tsx"
    },
    "/_auth": {
      "filePath": "_auth.tsx",
      "children": [
        "/_auth/application",
        "/_auth/console-board",
        "/_auth/recharge-management"
      ]
    },
    "/login": {
      "filePath": "login.tsx"
    },
    "/_auth/application": {
      "filePath": "_auth/application.tsx",
      "parent": "/_auth",
      "children": [
        "/_auth/application/detail",
        "/_auth/application/$applicationId/order",
        "/_auth/application/$applicationId/overview",
        "/_auth/application/$applicationId/settings",
        "/_auth/application/$applicationId/team",
        "/_auth/application/$applicationId/user"
      ]
    },
    "/_auth/console-board": {
      "filePath": "_auth/console-board.tsx",
      "parent": "/_auth"
    },
    "/_auth/recharge-management": {
      "filePath": "_auth/recharge-management.tsx",
      "parent": "/_auth"
    },
    "/_auth/application/detail": {
      "filePath": "_auth/application/detail.tsx",
      "parent": "/_auth/application"
    },
    "/_auth/application/$applicationId/order": {
      "filePath": "_auth/application/$applicationId.order.tsx",
      "parent": "/_auth/application"
    },
    "/_auth/application/$applicationId/overview": {
      "filePath": "_auth/application/$applicationId.overview.tsx",
      "parent": "/_auth/application"
    },
    "/_auth/application/$applicationId/settings": {
      "filePath": "_auth/application/$applicationId.settings.tsx",
      "parent": "/_auth/application"
    },
    "/_auth/application/$applicationId/team": {
      "filePath": "_auth/application/$applicationId.team.tsx",
      "parent": "/_auth/application"
    },
    "/_auth/application/$applicationId/user": {
      "filePath": "_auth/application/$applicationId.user.tsx",
      "parent": "/_auth/application"
    }
  }
}
ROUTE_MANIFEST_END */
