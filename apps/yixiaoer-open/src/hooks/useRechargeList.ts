import { useQuery } from '@tanstack/react-query';
import { rechargeList } from '@/api/recharge';
import { PageQuery } from '@/types';
import { RechargeParams } from '@/types/recharge';

export function useRechargeList(params: PageQuery & RechargeParams) {
  return useQuery({
    queryKey: ['rechargeList', params],
    queryFn: () => rechargeList(params),
    // 可以添加一些默认配置
    staleTime: 5 * 60 * 1000, // 5分钟内数据被认为是新鲜的
    gcTime: 10 * 60 * 1000, // 10分钟后清理缓存
  });
}
