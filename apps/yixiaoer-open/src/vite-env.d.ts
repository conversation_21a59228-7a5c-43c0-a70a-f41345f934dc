/// <reference types="vite/client" />
interface ImportMetaEnv {
  readonly VITE_APP_TITLE: string;
  readonly VITE_API_BASE_URL: string;
  readonly VITE_WSS_URL: string;
  readonly VITE_WSS_PATH: string;
  readonly VITE_DB_PREFIX: string;
  readonly VITE_STORE_PREFIX: string;
  readonly VITE_APP_VERSION: string;
  readonly VITE_APP_INVITATION: string;
  readonly VITE_CLIENT_KEY: string;
  readonly VITE_FRONTEND_URL: string;
  // 更多环境变量...
}

interface ImportMeta {
  readonly env: ImportMetaEnv;
}
