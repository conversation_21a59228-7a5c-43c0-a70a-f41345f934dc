<?xml version="1.0" encoding="UTF-8"?>
<svg width="40px" height="40px" viewBox="0 0 40 40" version="1.1" xmlns="http://www.w3.org/2000/svg">
    <defs>
        <linearGradient x1="0%" y1="100%" x2="100%" y2="0%" id="purpleGradient">
            <stop stop-color="#7C3AED" offset="0%"></stop>
            <stop stop-color="#A855F7" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g>
        <!-- 背景圆形 -->
        <circle cx="20" cy="20" r="20" fill="url(#purpleGradient)"/>
        
        <!-- 白色的樱桃图案 -->
        <g transform="translate(8, 6)">
            <!-- 樱桃主体 -->
            <ellipse cx="8" cy="18" rx="6" ry="8" fill="white"/>
            <ellipse cx="16" cy="20" rx="5" ry="7" fill="white"/>
            
            <!-- 樱桃内部阴影 -->
            <ellipse cx="8" cy="18" rx="3" ry="4" fill="#7C3AED" opacity="0.3"/>
            <ellipse cx="16" cy="20" rx="2.5" ry="3.5" fill="#7C3AED" opacity="0.3"/>
            
            <!-- 樱桃柄 -->
            <path d="M 8 10 Q 6 8 4 6 Q 2 4 1 2" stroke="white" stroke-width="2" fill="none" stroke-linecap="round"/>
            <path d="M 16 12 Q 18 10 20 8 Q 22 6 23 4" stroke="white" stroke-width="2" fill="none" stroke-linecap="round"/>
        </g>
    </g>
</svg>
